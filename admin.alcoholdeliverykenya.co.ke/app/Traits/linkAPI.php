<?php


namespace App\Traits;

use Illuminate\Config\Repository;
use Illuminate\Support\Facades\Session;

trait linkAPI
{
    /**
     * @return Repository|mixed
     */
    public function apiUrl()
    {
        return config('app.api_url');
    }

    /**
     * @return Repository|mixed
     */
    public function token()
    {
        return Session::get('token');
    }

    public function commonHeaders()
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

}
