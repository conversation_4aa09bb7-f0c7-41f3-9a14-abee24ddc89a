<?php


namespace App\Helpers;


class GeneralHelper
{
    /*
     * Generate random string
     */
    public static function generateRandomString($length = 8)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }

    /**
     * @param  string  $accountNumber
     * @return string|string[]
     */
    public static function hideAccountNumber(string $accountNumber)
    {
        if(strlen($accountNumber)==13) {
            $accountNumber[3] = 'X';
            $accountNumber[4] = 'X';
            $accountNumber[5] = 'X';
            $accountNumber[6] = 'X';
            return $accountNumber;
        }
        else{
            $accountNumber[5] = 'X';
            $accountNumber[6] = 'X';
            $accountNumber[7] = 'X';
            $accountNumber[8] = 'X';
            return $accountNumber;
        }
    }

    /**
     * Format phoneNumber and remove whitespaces
     * @param  string  $phoneNumber
     * @return string|string[]
     */
    public static function trimPhoneNumber(string $phoneNumber)
    {
       return  str_replace(' ', '', $phoneNumber);
    }
}
