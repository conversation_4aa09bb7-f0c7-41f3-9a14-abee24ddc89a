<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class DistillerMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (!empty(session('authenticated'))) {
            $request->session()->put('authenticated', time());
            return $next($request);
        }
        return redirect('/')->with('warning','You are not authenticated!');
//        return redirect('/');
    }
}
