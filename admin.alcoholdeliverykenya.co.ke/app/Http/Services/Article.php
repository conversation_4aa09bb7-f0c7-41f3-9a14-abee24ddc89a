<?php


namespace App\Http\Services;


use App\Traits\linkAPI;
use Illuminate\Support\Facades\Http;

class Article
{
    use linkAPI;

    public function articles()
    {
        return Http::withHeaders($this->commonHeaders())
            ->get($this->apiUrl() . '/articles')->object()->result;
    }
    public function article($param)
    {
        return Http::withHeaders($this->commonHeaders())
            ->get($this->apiUrl() . '/articles/view/'.$param)->object()->result;
    }
}
