<?php


namespace App\Http\Services;


use App\Traits\linkAPI;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class Product
{
    use linkAPI;

    /**
     * @param $params
     * @return Response
     */
    public function products($params=null)
    {
        return Http::withHeaders($this->commonHeaders())
            ->get($this->apiUrl() . '/all-products',$params)->object()->result;
    }
    public function product($param)
    {
        return Http::withHeaders($this->commonHeaders())
            ->get($this->apiUrl() . '/products/view/'.$param)->object()->result;
    }
}
