<?php

namespace App\Http\Services;

use App\Traits\linkAPI;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class Category {
	use linkAPI;

	/**
	 * @return Response
	 */
	public function categories() {
		return Http::withHeaders($this->commonHeaders())
			->get($this->apiUrl() . '/categories')->object()->result;
	}
	public function pageCategories() {
		return Http::withHeaders($this->commonHeaders())
			->get($this->apiUrl() . '/page-categories')->object()->result->page_categories;
	}
	public function freeCategories() {
		return Http::withHeaders($this->commonHeaders())
			->get($this->apiUrl() . '/page-categories')->object()->result->free_categories;
	}
	public function getSubcategory($name) {
		$categories = collect((new Category())->categories());
		return $categories->where('name', $name)->pluck('subcategories')->flatten();
	}
	//get category by id
	public function category($param) {
		return Http::withHeaders($this->commonHeaders())
			->get($this->apiUrl() . '/categories/view/' . $param)->object()->result;
	}
}
