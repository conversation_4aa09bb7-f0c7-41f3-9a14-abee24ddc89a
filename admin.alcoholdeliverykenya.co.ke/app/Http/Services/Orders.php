<?php


namespace App\Http\Services;


use App\Traits\linkAPI;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class Orders
{
    use linkAPI;

    /**
     * show orders
     */
    public function orders()
    {
        return Http::withHeaders($this->commonHeaders())
            ->withToken($this->token())
            ->get($this->apiUrl() . '/orders')->object()->result;
    }

    /**
     * Get single order
     * @param $param
     * @return mixed
     */
    public function order($param)
    {
        return Http::withHeaders($this->commonHeaders())
            ->withToken($this->token())
            ->get($this->apiUrl() . '/orders/view/' . $param)->object()->result;
    }

    /**
     * Get pending orders
     */
    public function pending()
    {
        return Http::withHeaders($this->commonHeaders())
            ->withToken($this->token())
            ->get($this->apiUrl() . '/orders/pending')->object()->result;
    }

    /**
     * Get completed orders
     */
    public function completed()
    {
        return Http::withHeaders($this->commonHeaders())
            ->withToken($this->token())
            ->get($this->apiUrl() . '/orders/completed')->object()->result;
    }
}
