<?php


namespace App\Http\Services;


use App\Traits\linkAPI;
use Illuminate\Support\Facades\Http;

class Users
{
    use linkAPI;
    public function users()
    {
        return Http::withHeaders($this->commonHeaders())
            ->withToken($this->token())
            ->get($this->apiUrl() . '/users/all')->object()->result;
    }
    public function user($param)
    {
        return Http::withHeaders($this->commonHeaders())
            ->withToken($this->token())
            ->get($this->apiUrl() . '/users/show/'.$param)->object()->result;
    }
}
