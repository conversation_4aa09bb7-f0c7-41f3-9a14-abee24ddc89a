<?php


namespace App\Http\Services;


use Illuminate\Support\Facades\Http;
use App\Traits\linkAPI;
class Carousel
{
    use linkAPI;
    /**
     *Get carousels
     */
    public  function carousels(){
        return Http::withHeaders($this->commonHeaders())
            ->get($this->apiUrl() . '/carousels')->object()->result;
    }
    public function carousel($param)
    {
        return Http::withHeaders($this->commonHeaders())
            ->get($this->apiUrl() . '/carousels/view/'.$param)->object()->result;
    }
}
