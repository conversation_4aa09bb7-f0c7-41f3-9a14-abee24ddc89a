<?php


namespace App\Http\Services;


use App\Traits\linkAPI;
use Illuminate\Support\Facades\Http;

class Brand
{
    use linkAPI;
    public function brands()
    {
        return Http::withHeaders($this->commonHeaders())
            ->get($this->apiUrl() . '/brands/all')->object()->result;
    }
    public function brand($param)
    {
        return Http::withHeaders($this->commonHeaders())
            ->get($this->apiUrl() . '/brands/view/'.$param)->object()->result;
    }
}
