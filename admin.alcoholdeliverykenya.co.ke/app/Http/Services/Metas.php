<?php


namespace App\Http\Services;


use App\Traits\linkAPI;
use Illuminate\Support\Facades\Http;

class Metas
{
    use linkAPI;

    public function metas()
    {
        return Http::withHeaders($this->commonHeaders())
            ->get($this->apiUrl() . '/metas')->object()->result;
    }
    public function meta($param)
    {
        return Http::withHeaders($this->commonHeaders())
            ->get($this->apiUrl() . '/metas/show/'.$param)->object()->result;
    }
}
