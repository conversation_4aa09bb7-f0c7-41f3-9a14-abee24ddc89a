<?php

namespace App\Http\Controllers;

use App\Http\Services\Carousel;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\View\View;

class CarouselController extends Controller
{
    public function index()
    {
        $carousels = collect((new Carousel())->carousels());
        return view('pages.carousels.index', compact('carousels'));
    }

    /**
     * @param $id
     * @return Application|Factory|View
     */
    public function show($id)
    {
        $carousel = (new Carousel())->carousel($id);
        //dd($carousel);
        return view('pages.carousels.show', compact('carousel'));
    }

    public function create()
    {
        return view('pages.carousels.create');
    }

    public function store(Request $request): RedirectResponse
    {
        // dd($request->all());
        $data = collect($request->all());
        // set image
        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->getClientOriginalName();
        } else {
            $data['image'] = '';
        }
        // set mobile image
        if ($request->hasFile('mobile_image')) {
            $data['mobile_image'] = $request->file('mobile_image')->getClientOriginalName();
        } else {
            $data['mobile_image'] = '';
        }
        $response = Http::withHeaders($this->commonHeaders())
            ->withToken($this->token())
            ->post($this->apiUrl() . '/carousels/create', $data->toArray());
        //dd($response->object());
        if ($response->object()->message == 'error') {
            return redirect()->back()->with('error', 'An error occurred,check that all required fields are filled before submission!');
        } else {
            return redirect()->route('carousels')->with('success', $response->object()->result);
        }
    }

    /**
     * delete category
     * @param $id
     * @return RedirectResponse
     */
    public function destroy($id)
    {
        $response = Http::withHeaders($this->commonHeaders())
            ->withToken($this->token())
            ->delete($this->apiUrl() . '/carousels/delete/' . $id);
        if ($response->object()->message == 'error') {
            return redirect()->back()->with('error', $response->object()->result);
        } else {
            return redirect()->back()->with('success', $response->object()->result);
        }
    }
}
