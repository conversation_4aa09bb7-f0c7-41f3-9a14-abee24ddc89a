<?php

namespace App\Http\Controllers;

use App\Http\Services\Users;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\View\View;

class UserController extends Controller
{
    public function index()
    {
        return view('pages.users.index');
    }
    /**
     * @return Application|Factory|View
     */
    public function create()
    {
        return view('pages.users.create');
    }

    public function store(Request $request): RedirectResponse
    {
        //dd($request->all());
        $data = collect($request->all());
        $response = Http::withHeaders($this->commonHeaders())
            ->withToken($this->token())
            ->post($this->apiUrl() . '/users/create-user', $data->toArray());
        //dd($response->object());
        if ($response->object()->message == 'error') {
            return redirect()->back()->with('error', 'An error occurred,check that all required fields are filled before submission!');
        } else {
            return redirect()->route('users')->with('success', $response->object()->message);
        }
    }
    /**
     * @param $id
     * @return Application|Factory|View
     */
    public function edit($id)
    {
        $user = (new Users())->user($id);
        //dd($user);
        return view('pages.users.edit', compact('user'));
    }

    public function update(Request $request, $id): RedirectResponse
    {
        //dd($request->all());
        $data = collect($request->all());
        $response = Http::withHeaders($this->commonHeaders())
            ->withToken($this->token())
            ->put($this->apiUrl() . '/users/update/' . $id, $data->toArray());
        //dd($response->object());
        if ($response->object()->message == 'error') {
            return redirect()->back()->with('error', 'An error occurred,check that all required fields are filled before submission!');
        } else {
            return redirect()->route('users')->with('success', $response->object()->message);
        }
    }
         /**
     * delete category
     * @param $id
     * @return RedirectResponse
          */
    public function deleteUser($id): RedirectResponse
    {
        $response = Http::withHeaders($this->commonHeaders())
            ->withToken($this->token())
            ->delete($this->apiUrl() . '/users/delete/' . $id);
        if ($response->object()->message == 'error') {
            return redirect()->back()->with('error', $response->object()->message);
        } else {
            return redirect()->route('users')->with('success', $response->object()->message);
        }

    }

}
