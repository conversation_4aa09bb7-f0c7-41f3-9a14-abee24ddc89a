<?php

namespace App\Http\Controllers;

use App\Http\Services\Brand;
use App\Http\Services\Category;
use App\Http\Services\GeneralService;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\View\View;

class BrandController extends Controller
{
    public function index()
    {
        return view('pages.brands.index');
    }

    /**
     * @param $id
     * @return Application|Factory|View
     */
    public function show($id)
    {
        $brand = (new Brand())->brand($id);
        //dd($meta);
        return view('pages.brands.show', compact('brand'));
    }

    public function create()
    {
        $categories = collect((new Category())->categories());
        $countries = collect((new GeneralService())->countries());
        return view('pages.brands.create', compact('categories', 'countries'));
    }

    public function store(Request $request): \Illuminate\Http\RedirectResponse
    {
        // dd($request->all());
        $data = collect($request->all());
        $response = Http::withHeaders($this->commonHeaders())
            ->withToken($this->token())
            ->post($this->apiUrl() . '/brands/create', $data->toArray());
        //dd($response->object());
        if ($response->object()->message == 'error') {
            return redirect()->back()->with('error', 'An error occurred,check that all required fields are filled before submission!');
        } else {
            return redirect()->route('brands')->with('success', $response->object()->result);
        }
    }

    /**
     * @param $id
     * @return Application|Factory|View
     */
    public function edit($id)
    {
        $brand = (new Brand())->brand($id);
        $categories = collect((new Category())->categories());
        $countries = collect((new GeneralService())->countries());
        //dd($brand);
        return view('pages.brands.edit', compact('brand', 'categories', 'countries'));
    }

    public function update(Request $request, $id): \Illuminate\Http\RedirectResponse
    {
        // dd($request->all());
        $data = collect($request->all());
        $response = Http::withHeaders($this->commonHeaders())
            ->withToken($this->token())
            ->put($this->apiUrl() . '/brands/update/' . $id, $data->toArray());
        //dd($response->object());
        if ($response->object()->message == 'error') {
            //dd($response->object());
            return redirect()->back()->with('error', 'An error occurred,check that all required fields are filled before submission!');
        } else {
            return redirect()->route('brands')->with('success', $response->object()->result);
        }
    }
    /**
     * delete  brand
     * @param $id
     * @return RedirectResponse
     */
    public function destroy($id): RedirectResponse
    {
        $response = Http::withHeaders($this->commonHeaders())
            ->withToken($this->token())
            ->delete($this->apiUrl() . '/brands/delete/' . $id);
        if ($response->object()->message == 'error') {
            return redirect()->back()->with('error', $response->object()->result);
        } else {
            return redirect()->back()->with('success', $response->object()->result);
        }
    }
}
