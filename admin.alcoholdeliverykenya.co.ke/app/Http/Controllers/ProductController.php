<?php

namespace App\Http\Controllers;

use App\Http\Services\Brand;
use App\Http\Services\Category;
use App\Http\Services\GeneralService;
use App\Http\Services\Product;
use App\Http\Services\Quantities;
use Exception;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\View\View;

class ProductController extends Controller {
	/**
	 * @param Request $request
	 * @return Application|Factory|View
	 */
	public function index(Request $request) {
		return view('pages.products.index');
	}

	/**
	 * @param $id
	 * @return Application|Factory|View
	 */
	public function show($id) {
		$product = (new Product())->product($id);
		//dd($product);
		return view('pages.products.show', compact('product'));
	}

	/**
	 * @param $id
	 * @return Application|Factory|View
	 */
	public function edit($id) {
		$product = (new Product())->product($id);
		// dd($product);
		$brands = collect((new Brand())->brands());
		$categories = collect((new Category())->categories());
		$quantities = collect((new Quantities())->quantities());
		$countries = collect((new GeneralService())->countries());
		//dd($product);
		return view('pages.products.edit', compact('product', 'brands', 'categories', 'quantities', 'countries'));
	}

	/**
	 * @return Application|Factory|View
	 */
	public function create() {
		$brands = collect((new Brand())->brands());
		$categories = collect((new Category())->categories());
		$countries = collect((new GeneralService())->countries());
		$quantities = collect((new Quantities())->quantities());

		return view('pages.products.create', compact('brands', 'categories', 'quantities', 'countries'));
	}

	public function store(Request $request) {
		//  dd($request->all());
		try {
			$quantity = collect($request->quantity);
			$price = collect($request->price);
			$wholesale = collect($request->wholesalePrice);
			$discount = collect($request->discount);
			$looped = [];
			foreach ($quantity as $key => $row) {
				$looped[] = [
					'quantity' => $row,
					'price' => $price[$key],
					'wholesalePrice' => $wholesale[$key],
					'discount' => $discount[$key],
				];

			}

			//dd(collect($looped)->toJson());
			$tags = explode(',', $request->tags);
			$quantities = collect($looped)->toArray();
			$data = collect($request->all());
			$data['tags'] = collect($tags)->toArray();
			$data['label'] = collect($request->label)->toArray();
			$data['subcategory'] = collect($request->subcategory)->toArray();
			$data['quantities'] = $quantities;
			$data['available'] = true;
			$data['featured'] = false;
			$data['published'] = true;
			$data->forget(['quantity', 'price', 'wholesalePrice', 'discount', '_token']);
			if ($request->hasFile('image')) {
				$data['image'] = $request->file('image')->getClientOriginalName();
			} else {
				$data['image'] = '';
			}
			$final_data = $data->toArray();
			dd($final_data);
			$response = Http::withHeaders($this->commonHeaders())
				->withToken($this->token())
				->post($this->apiUrl() . '/products/create', $final_data);
			if ($response->object()->message == 'error') {
				return redirect()->back()->with('error', 'An error occurred,check that all required fields are filled before submission!');
			} else {
				return redirect()->route('products')->with('success', $response->object()->result);
			}

		} catch (Exception $exception) {
			return redirect()->back()->with('error', $exception->getMessage());
		}
	}

	/*
		     * delete product
	*/
	/**
	 * @param $id
	 * @return RedirectResponse
	 */
	public function destroy($id) {
		$response = Http::withHeaders($this->commonHeaders())
			->withToken($this->token())
			->delete($this->apiUrl() . '/products/delete/' . $id);
		if ($response->object()->message == 'error') {
			return redirect()->route('products')->with('error', $response->object()->result);
		} else {
			return redirect()->route('products')->with('success', $response->object()->result);
		}
	}
	/*
		  * delete product
	*/
	/**
	 * @param Request $request
	 * @return RedirectResponse
	 */
	public function removeQuantity(Request $request) {
		$data = [
			'productId' => $request->productId,
			'quantity' => $request->quantity,
		];
		$response = Http::withHeaders($this->commonHeaders())
			->withToken($this->token())
			->get($this->apiUrl() . '/products/remove-quantity/', $data);
		if ($response->object()->message == 'error') {
			return redirect()->back()->with('error', $response->object()->result);
		} else {
			return redirect()->back()->with('success', $response->object()->result);
		}
	}

}
