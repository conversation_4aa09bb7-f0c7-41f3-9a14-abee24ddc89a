<?php

namespace App\Http\Controllers;

use App\Http\Services\Article;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\View\View;

class ArticleController extends Controller
{
    public function index()
    {
        return view('pages.blog.articles.index');
    }

    /**
     * @return Application|Factory|View
     */
    public function create()
    {
        return view('pages.blog.articles.create');
    }

    public function store(Request $request): RedirectResponse
    {
        //dd($request->all());
        $data = collect($request->all());
        //subcategories
        if ($request->has('tags') && $request->filled('tags')) {
            $tags = explode(',', $request->tags);
            $data['tags'] = $tags;
        } else {
            $data['tags'] = [];
        }
        // set photo
        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->getClientOriginalName();
        } else {
            $data['image'] = '';
        }
        //dd($data);
        $response = Http::withHeaders($this->commonHeaders())
            ->withToken($this->token())
            ->post($this->apiUrl() . '/articles/create', $data->toArray());
        //dd($response->object());
        if ($response->object()->message == 'error') {
            return redirect()->back()->with('error', 'An error occurred,check that all required fields are filled before submission!');
        } else {
            return redirect()->route('articles')->with('success', $response->object()->message);
        }
    }

    /**
     * @param $id
     * @return Application|Factory|View
     */
    public function show($id)
    {
        $article = (new Article())->article($id);
        //dd($article);
        return view('pages.blog.articles.show', compact('article'));
    }

    /**
     * @param $id
     * @return Application|Factory|View
     */
    public function edit($id)
    {
        $article = (new Article())->article($id);
        //dd($article);
        return view('pages.blog.articles.edit', compact('article'));
    }

    public function update(Request $request, $id): RedirectResponse
    {
        //dd($request->all());
        $data = collect($request->all());
        //subcategories
        if ($request->has('tags') && $request->filled('tags')) {
            $tags = explode(',', $request->tags);
            $data['tags'] = $tags;
        } else {
            $data['tags'] = [];
        }
        // set photo
        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->getClientOriginalName();
        } else {
            $data['image'] = '';
        }
        //dd($data);
        $response = Http::withHeaders($this->commonHeaders())
            ->withToken($this->token())
            ->put($this->apiUrl() . '/articles/update/' . $id, $data->toArray());
        // dd($response->object());
        if ($response->object()->message == 'error') {
            return redirect()->back()->with('error', 'An error occurred,check that all required fields are filled before submission!');
        } else {
            return redirect()->route('articles')->with('success', $response->object()->result);
        }
    }

    /**
     * delete article
     * @param $id
     * @return RedirectResponse
     */
    public function destroy($id): RedirectResponse
    {
        $response = Http::withHeaders($this->commonHeaders())
            ->withToken($this->token())
            ->delete($this->apiUrl() . '/articles/delete/' . $id);
        if ($response->object()->message == 'error') {
            return redirect()->back()->with('error', $response->object()->result);
        } else {
            return redirect()->back()->with('success', $response->object()->result);
        }
    }
}
