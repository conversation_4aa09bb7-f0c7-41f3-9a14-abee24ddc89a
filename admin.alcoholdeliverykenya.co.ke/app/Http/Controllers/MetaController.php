<?php

namespace App\Http\Controllers;

use App\Http\Services\Category;
use App\Http\Services\Metas;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\View\View;

class MetaController extends Controller {
	/**
	 * @return Application|Factory|View
	 */
	public function index() {
		return view('pages.blog.metas.index');
	}

	/**
	 * create meta
	 * @return Application|Factory|View
	 */
	public function create() {
		$categories = collect((new Category())->freeCategories());
		//dd($categories);
		return view('pages.blog.metas.create', compact('categories'));
	}

	/**
	 * store meta
	 * @param Request $request
	 * @return RedirectResponse
	 */
	public function store(Request $request): RedirectResponse{
		// dd($request->all());
		$data = collect($request->all());
		$data['isCategory'] = false;
		$response = Http::withHeaders($this->commonHeaders())
			->withToken($this->token())
			->post($this->apiUrl() . '/metas/create', $data->toArray());
		//dd($response->object());
		if ($response->object()->message == 'error') {
			return redirect()->back()->with('error', 'An error occurred,check that all required fields are filled before submission!');
		} else {
			return redirect()->route('metas')->with('success', $response->object()->result);
		}
	}

	/**
	 * @param $id
	 * @return Application|Factory|View
	 */
	public function show($id) {
		$meta = (new Metas())->meta($id);
		//dd($meta);
		return view('pages.blog.metas.show', compact('meta'));
	}

	/**
	 * @param $id
	 * @return Application|Factory|View
	 */
	public function edit($id) {
		$meta = (new Metas())->meta($id);
		$categories = collect((new Category())->pageCategories());
		//dd($meta);
		return view('pages.blog.metas.edit', compact('meta', 'categories'));
	}

	/**
	 * @param Request $request
	 * @param $id
	 * @return RedirectResponse
	 */
	public function update(Request $request, $id): RedirectResponse{
		// dd($request->all());
		$data = collect($request->all());
		$data['isCategory'] = false;
		$response = Http::withHeaders($this->commonHeaders())
			->withToken($this->token())
			->put($this->apiUrl() . '/metas/update/' . $id, $data->toArray());
		//dd($response->object());
		if ($response->object()->message === 'error') {
			//dd($response->object());
			return redirect()->back()->with('error', 'An error occurred,check that all required fields are filled before submission!');
		} else {
//            dd($response->object());
			return redirect()->route('metas')->with('success', $response->object()->result);
		}
	}
	/**
	 * delete  meta
	 * @param $id
	 * @return RedirectResponse
	 */
	public function destroy($id): RedirectResponse{
		$response = Http::withHeaders($this->commonHeaders())
			->withToken($this->token())
			->delete($this->apiUrl() . '/metas/delete/' . $id);
		if ($response->object()->message === 'error') {
			return redirect()->back()->with('error', $response->object()->result);
		} else {
			return redirect()->back()->with('success', $response->object()->result);
		}
	}
}
