<?php

namespace App\Http\Controllers;

use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;

class LoginController extends Controller
{
    /**
     * Process Login
     * @param Request $request
     * @return RedirectResponse
     */

    public function processLogin(Request $request)
    {
        //dd($request->all());
        $validatedData = Validator::make(
            $request->all(),
            [
                'email'=> 'required|email',
                'password'=> 'required',
            ]
        );
        try {
            // dd(Auth::attempt(['email' => $phone, 'password' => $password, 'status' => 1]));
            $data = [
                "email"=>$validatedData->validated()['email'],
                "password"=>$validatedData->validated()['password'],
            ];
            //dd($data);
            $response = Http::withHeaders($this->commonHeaders())->post($this->apiUrl() . '/auth/login', $data);
         //   dd($response->object());
            if (is_null($response->object()) || $response->object()->message == 'error') {
                return redirect()->back()->with('error', $response->object()->result ?? 'an error occurred!');
            } else {
                $token=$response->object()->result->accessToken;
                echo "<script>document.write(localStorage.setItem('radius', '".$token."'))</script>";
                $request->session()->put(
                    [
                        'authenticated' => time(),
                        'user' => $response->object()->result->user,
                        'token' => $token
                    ]
                );
                return redirect()->intended('/main/dashboard');
            }
        } catch (Exception $e) {
            return redirect()->back()->with('error', 'Could not login to your account,invalid email or password!');
        }
    }

    /**
     * Do Logout
     * @return RedirectResponse|Redirector
     */
    public function getLogout()
    {
        try {
//            Auth::logout();
            session()->forget(['authenticated', 'user', 'token']);
            echo "<script>document.write(localStorage.removeItem('radius'))</script>";
            return redirect('/')->with('success', 'Logged Out.');
        } catch (Exception $e) {
            return redirect()->back()->with('error', 'The system could not log you out, try again!');
        }
    }
}
