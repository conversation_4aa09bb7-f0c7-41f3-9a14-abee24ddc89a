<?php

namespace App\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Http;

class ReviewsController extends Controller {
	public function index() {
		return view('pages.reviews.index');
	}
	/**
	 * delete  brand
	 * @param $id
	 * @return RedirectResponse
	 */
	public function approve($id): RedirectResponse{
		$response = Http::withHeaders($this->commonHeaders())
			->withToken($this->token())
			->put($this->apiUrl() . '/ratings/approve/' . $id);
		if ($response->object()->message == 'error') {
			return redirect()->back()->with('error', $response->object()->message);
		} else {
			return redirect()->back()->with('success', $response->object()->message);
		}
	}
}
