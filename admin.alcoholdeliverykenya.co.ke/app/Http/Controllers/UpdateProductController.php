<?php

namespace App\Http\Controllers;

use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class UpdateProductController extends Controller {
	public function update(Request $request, $id): RedirectResponse
    {
		//dd($request->all());
		try {
			$quantity = $request->quantity;
			$price = $request->price;
			$wholesale = $request->wholesalePrice;
			$discount = $request->discount;
			$looped = [];
			foreach ($quantity as $key => $row) {
				$looped[] = [
					'quantity' => $row,
					'price' => $price[$key],
					'wholesalePrice' => $wholesale[$key],
					'discount' => $discount[$key],
				];

			}

			$quantities = collect($looped)->filter(function ($item) {
				return $item['quantity'] != null;
			});
			$labels = collect($request->label)->filter(function ($item) {
				return $item != null;
			});
			$data = collect($request->all());
			//check tags
			if ($request->has('tags') && $request->filled('tags')) {
				$tags = explode(',', $request->tags);
				$data['tags'] = collect($tags)->toArray();
			} else {
				unset($data['tags']);
			}
			//check display categories
			if ($request->has('displayCategory') && $request->filled('displayCategory')) {
				$display_categories = explode(',', $request->displayCategory);
				$data['displayCategory'] = collect($display_categories)->toArray();
			}
			//check labels
			if ($labels->isNotEmpty()) {
				$data['label'] = collect($request->label)->toArray();
			} else {
				unset($data['label']);
			}
			//features
			if ($request->has('features') && $request->filled('features')) {
				$features = explode(',', $request->features);
				$data['features'] = collect($features)->toArray();
			} else {
				unset($data['features']);
			}
			$data['label'] = collect($request->label)->toArray();
			$data['subcategory'] = collect($request->subcategory)->toArray();
			//
			if ($quantities->isEmpty()) {
				unset($data['quantities']);
			} else {
				$data['quantities'] = $quantities;
			}

			$data['available'] = (boolean) $request->available;
			$data['featured'] = (boolean) $request->featured;
			$data['published'] = (boolean) $request->published;
			$data->forget(['quantity', 'price', 'wholesalePrice', 'discount', '_token']);
			if ($request->hasFile('image')) {
				$data['image'] = $request->file('image')->getClientOriginalName();
			} else {
				$data['image'] = '';
			}
			$final_data = $data->toArray();
			//dd($final_data);
			$response = Http::withHeaders($this->commonHeaders())
				->withToken($this->token())
				->put($this->apiUrl() . '/products/update/' . $id, $final_data);
			//dd($response->object());
			if ($response->object()->message == 'error') {
				return redirect()->back()->with('error', 'An error occurred,check that all required fields are filled before submission!');
			} else {
				return redirect()->route('products')->with('success', $response->object()->message);
			}

		} catch (Exception $exception) {
			return redirect()->back()->with('error', $exception->getMessage());
		}
	}
}
