<?php

namespace App\Http\Controllers;

use App\Http\Services\Orders;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\View\View;
use Barryvdh\DomPDF\Facade as PDF;

class OrderController extends Controller
{
    /**
     * @return Application|Factory|View
     */
    public function index()
    {
        return view('pages.orders.index');
    }

    /**
     * @return Application|Factory|View
     */
    public function completed()
    {
        return view('pages.orders.completed');
    }

    /**
     * @return Application|Factory|View
     */
    public function all()
    {
        return view('pages.orders.all');
    }

    /**
     * @param $id
     * @return Application|Factory|View
     */
    public function show($id)
    {
        $order = (new Orders())->order($id);
//        dd($order);
        return view('pages.orders.show', compact('order'));
    }

    /**
     * @param $id
     * @return Application|Factory|View
     */
    public function view($id)
    {
        $order = (new Orders())->order($id);
        //dd($order);
        return view('pages.orders.view', compact('order'));
    }
    /**
     * @param $id
     * @return Application|Factory|View
     */
    public function downloadOrder($id)
    {
        $order = (new Orders())->order($id);
        //dd($order);
        $pdf = PDF::loadView('pages.orders.download', [
            'order' => $order,
        ])->setPaper('a4', 'portrait');
        return $pdf->download('order-'.$order->orderNo.'.pdf');
    }

    public function closeOrder($id): \Illuminate\Http\RedirectResponse
    {
        $response = Http::withHeaders($this->commonHeaders())
            ->withToken($this->token())
            ->put($this->apiUrl() . '/orders/close/' . $id);
        //dd($response->object()->result);
        if ($response->object()->message == 'error') {
            return redirect()->back()->with('error', 'An error occurred!');
        } else {
            return redirect()->back()->with('success', $response->object()->result);
        }
    }

    public function update(Request $request, $id): \Illuminate\Http\RedirectResponse
    {
        //dd($request->all());
        $data = [
            'amountPaid' => $request->amountPaid,
            'discountApplied' => $request->discountApplied,
            'deliveryDate' => $request->deliveryDate,
            'pending' => (boolean)$request->pending,
            'rejected' => (boolean)$request->rejected,
            'handled' => (boolean)$request->handled,
            'approved' => (boolean)$request->approved,
            'confirmed' => (boolean)$request->confirmed,
            'paid' => (boolean)$request->paid,
            'scheduled' => (boolean)$request->scheduled,
            'scheduleDate' => $request->scheduleDate,
        ];
        //dd($data);
        $response = Http::withHeaders($this->commonHeaders())
            ->withToken($this->token())
            ->put($this->apiUrl() . '/orders/update/' . $id, $data);
        //dd($response->object()->result);
        if ($response->object()->message == 'error') {
            return redirect()->back()->with('error', 'An error occurred,check that all required fields are filled before submission!');
        } else {
            return redirect()->route('orders')->with('success', $response->object()->result);
        }
    }
}
