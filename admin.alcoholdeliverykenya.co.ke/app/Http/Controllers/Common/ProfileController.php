<?php

namespace App\Http\Controllers\Common;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Session;

class ProfileController extends Controller
{
    public function index()
    {
        $info = Session::get('user');
        return view('pages.profile', compact('info'));
    }

    public function getPassword()
    {
        $info = Session::get('user');
        return view('pages.password-settings', compact('info'));
    }

    public function changePassword(Request $request)
    {
        //dd($request->all());
        $this->validate($request, [
            'currentPassword' => 'required',
            'newPassword' => 'required|min:6',
            'confirmPassword' => 'required|same:newPassword',
        ]);
        $data = [
            'currentPassword' => $request->currentPassword,
            'newPassword' => $request->newPassword,
        ];
        //dd($data);
        $response = Http::withHeaders($this->commonHeaders())
            ->withToken($this->token())
            ->post($this->apiUrl() . '/auth/change-password', $data);
        //dd($response->object());
        if ($response->object()->message == 'error') {
            return redirect()->route('change.password')->with('warning', $response->object()->result);
        } else {
            return redirect()->route('change.password')->with('success', $response->object()->result);
        }
    }

}
