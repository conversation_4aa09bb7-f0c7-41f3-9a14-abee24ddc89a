<?php

namespace App\Http\Controllers;

use App\Http\Services\Category;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;

class CategoryController extends Controller {
	public function index() {
		return view('pages.categories.index');
	}

	/**
	 * get subcategory by
	 * @param $name
	 * @return Collection
	 */
	public function getSubcategory($name): Collection{
		$categories = collect((new Category())->categories());
		return $categories->where('name', $name)->pluck('subcategories')->flatten();
	}

	public function create() {
		$menus = [
			'spirits',
			'beer',
			'wines',
			'more',
		];
		return view('pages.categories.create', compact('menus'));
	}

	public function store(Request $request): RedirectResponse{
		//dd($request->all());
		$data = collect($request->all());
		//subcategories
		if ($request->has('subcategories') && $request->filled('subcategories')) {
			$subcategories = collect(explode(',', $request->subcategories))
				->transform(function ($item) {
					return [
						'name' => $item ?? '',
					];
				})->values()->all();
			$data['subcategories'] = $subcategories;
		} else {
			$data['subcategories'] = [];
		}
		// set photo
		if ($request->hasFile('photo')) {
			$data['photo'] = $request->file('photo')->getClientOriginalName();
		} else {
			$data['photo'] = '';
		}
		// set mobile photo
		if ($request->hasFile('mobile_banner')) {
			$data['mobile_banner'] = $request->file('mobile_banner')->getClientOriginalName();
		} else {
			$data['mobile_banner'] = '';
		}
		$data['active'] = true;
		//dd($data);
		$response = Http::withHeaders($this->commonHeaders())
			->withToken($this->token())
			->post($this->apiUrl() . '/categories/create', $data->toArray());
		//dd($response->object());
		if ($response->object()->message == 'error') {
			return redirect()->back()->with('error', 'An error occurred,check that all required fields are filled before submission!');
		} else {
			return redirect()->back()->with('success', $response->object()->result);
		}
	}

	//show category by ID
	public function show($id) {
		$category = (new Category())->category($id);
		$menus = [
			'spirits',
			'beer',
			'wines',
			'more',
		];
		if (collect($category->subcategories)->isNotEmpty()) {
			$subcategories = $category->subcategories;
		}
		//dd($subcategories);
		return view('pages.categories.show', compact('menus', 'category'));
	}

	//edit category by ID
	public function edit($id) {
		$category = (new Category())->category($id);
		$menus = [
			'spirits',
			'beer',
			'wines',
			'more',
		];
		if (collect($category->subcategories)->isNotEmpty()) {
			$subcategories = implode(',', collect($category->subcategories)->pluck('name')->toArray());
		} else {
			$subcategories = '';
		}
		//dd($subcategories);
		return view('pages.categories.edit', compact('menus', 'category', 'subcategories'));
	}

	// update category

	/**
	 * @param Request $request
	 * @param $id
	 * @return RedirectResponse
	 */
	public function update(Request $request, $id): RedirectResponse{
		//dd($request->all());
		$data = collect($request->all());
		//subcategories
		if ($request->has('subcategories') && $request->filled('subcategories')) {
			$subcategories = collect(explode(',', $request->subcategories))
				->transform(function ($item) {
					return [
						'name' => $item ?? '',
					];
				})->values()->all();
			$data['subcategories'] = $subcategories;
		} else {
			$data['subcategories'] = [];
		}
		// set photo
		if ($request->hasFile('photo')) {
			$data['photo'] = $request->file('photo')->getClientOriginalName();
		} else {
			$data['photo'] = '';
		}
		// set mobile photo
		if ($request->hasFile('mobile_banner')) {
			$data['mobile_banner'] = $request->file('mobile_banner')->getClientOriginalName();
		} else {
			$data['mobile_banner'] = '';
		}
		$data['active'] = (boolean) $request->active;
		//dd($data);
		$response = Http::withHeaders($this->commonHeaders())
			->withToken($this->token())
			->put($this->apiUrl() . '/categories/update/' . $id, $data->toArray());
		//dd($response->object());
		if ($response->object()->message == 'error') {
			return redirect()->back()->with('error', 'An error occurred,check that all required fields are filled before submission!');
		} else {
			return redirect()->route('category')->with('success', $response->object()->result);
		}
	}

	/**
	 * delete category
	 * @param $id
	 * @return RedirectResponse
	 */
	public function destroy($id): RedirectResponse{

		//dd($id);
		$response = Http::withHeaders($this->commonHeaders())
			->withToken($this->token())
			->get($this->apiUrl() . '/categories/delete/' . $id);
		if ($response->object()->message == 'error') {
			return redirect()->back()->with('error', $response->object()->result);
		} else {
			return redirect()->back()->with('success', $response->object()->result);
		}
	}
}
