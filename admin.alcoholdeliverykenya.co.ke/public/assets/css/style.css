ul, ol {
    margin: 0px;
    padding: 0px;
    list-style-type: none;
}
.bg_cover {
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    width: 100%;
    height: 100%;
}
/*===== All Button Style =====*/

.main-btn {
    display: inline-block;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border: 1px solid #082e55;
    padding: 0 35px;
    font-size: 16px;
    font-family: 'Rubik', sans-serif;
    font-weight: 700;
    line-height: 50px;
    border-radius: 5px;
    color: #ffffff;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    z-index: 5;
    -webkit-transition: 0.4s ease-in-out;
    transition: 0.4s ease-in-out;
    background-color: #082e55;
}
.main-btn:hover {
    color: #ffffff;
    border-color: #082e55;
    background-color: #082e55;
}
.main-btn-2 {
    color: #082e55;
    border-color: #792326;
    background-color: #792326;
}
.main-btn-2:hover {
    color: #792326;
    background-color: #082e55;
    border-color: #082e55;
}
/*===== All Section Title Style =====*/

.section-title {}
.section-title h5 {
    color: #7A2327;
    position: relative;
    padding-bottom: 12px;
}
.section-title h5::before {
    content: '';
    position: absolute;
    width: 35px;
    height: 2px;
    background-color: #092e56;
    bottom: 0;
    left: 0;
}
.section-title h2 {
    font-size: 48px;
    color: #000;
    padding-top: 10px;
}
/*=====================================================
    22. COURSES PAGE css
======================================================*/

.tab-content {}
.tab-content .single-course .course-teacher .name {
    bottom: 0;
}
.courses-top-search {
    background-color: #fff;
    padding: 15px 30px;
    border-radius: 5px;
    overflow: hidden;
}
.courses-top-search .nav {
    margin-top: 5px;
}
.courses-top-search .nav .nav-item {
    margin-right: 15px;
    font-size: 15px;
    color: #8a8a8a;
}
.courses-top-search .nav .nav-item a {
    font-size: 16px;
    color: #8a8a8a;
}
.courses-top-search .nav .nav-item a.active {
    color: #082e55;
}
.courses-search {
    position: relative;
}
.courses-search input {
    height: 30px;
    width: 240px;
    background-color: #f6f6f6;
    border: 0;
    color: #8a8a8a;
    font-size: 14px;
    border-radius: 5px;
    padding: 0 20px;
}
.courses-search button {
    position: absolute;
    top: 0;
    right: 15px;
    padding: 0;
    height: 30px;
    font-size: 15px;
    color: #8a8a8a;
    border: 0;
    background: none;
    cursor: pointer;
}
#courses-list .single-course .thum .price {
    right: -25px;
    bottom: auto;
    top: 30px;
}
.courses-pagination {}
.courses-pagination .pagination {}
.courses-pagination .pagination .page-item {
    margin: 0 5px;
}
.courses-pagination .pagination .page-item a {
    font-size: 15px;
    color: #792326;
    width: 40px;
    height: 40px;
    line-height: 36px;
    border: 2px solid #aaa;
    border-radius: 5px;
    text-align: center;
    -webkit-transition: all 0.4s linear;
    transition: all 0.4s linear;
}
.courses-pagination .pagination .page-item a.active, .courses-pagination .pagination .page-item a:hover {
    background-color: #082e55;
    border-color: #082e55;
}
/*=====================================================
    23. COURSE SINGEl PAGE css
======================================================*/

.courses-single-left {
    padding: 45px 50px;
    background-color: #fff;
}
.courses-single-left .title {}
.courses-single-left .title h3 {
    font-size: 30px;
    color: #000;
    font-weight: 600;
    padding-bottom: 25px;
}
.courses-single-left .course-terms {}
.courses-single-left .course-terms>ul>li {
    display: inline-block;
    margin-right: 60px;
}
.courses-single-left .course-terms>ul li:last-child {
    margin-right: 0;
}
.courses-single-left .course-terms ul li .teacher-name {
    position: relative;
}
.courses-single-left .course-terms ul li .teacher-name .thum {
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    left: 0;
}
.courses-single-left .course-terms ul li .teacher-name .thum img {
    border-radius: 50%;
}
.courses-single-left .course-terms ul li .teacher-name .name {
    padding-left: 60px;
}
.courses-single-left .course-terms ul li .review span, .courses-single-left .course-terms ul li .teacher-name .name span, .courses-single-left .course-terms ul li .course-category span {
    color: #8a8a8a;
    font-size: 14px;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
}
.courses-single-left .course-terms ul li .teacher-name .name h6, .courses-single-left .course-terms ul li .course-category h6 {
    font-size: 16px;
    color: #24486e;
    font-weight: 700;
}
.courses-single-left .course-terms ul li .review {}
.courses-single-left .course-terms ul li .review ul li {
    display: inline-block;
}
.courses-single-left .course-terms ul li .review ul li a {
    font-size: 14px;
    color: #082e55;
}
.courses-single-left .course-terms ul li .review ul li.rating {
    font-size: 14px;
    color: #000;
    font-weight: 600;
    margin-left: 13px;
}
.courses-single-image {}
.courses-single-image img {
    width: 100%;
}
.courses-tab {
    border: 1px solid #edf0f2;
    border-radius: 5px;
}
.courses-tab .nav {}
.courses-tab .nav .nav-item {}
.courses-tab .nav .nav-item a {
    font-size: 16px;
    color: #24486e;
    font-weight: 600;
    font-family: 'Montserrat', sans-serif;
    padding: 20px 0;
    display: block;
    background-color: #edf0f2;
}
.courses-tab .nav .nav-item a.active {
    background-color: #fff;
}
.overview-description {
    padding: 0px 30px 25px;
}
.overview-description .single-description {}
.overview-description .single-description h6 {
    font-size: 18px;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    color: #000;
    padding-bottom: 10px;
}
.curriculum-cont {
    padding: 25px 30px;
}
.curriculum-cont .title h6 {
    font-size: 18px;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    color: #000;
    padding-bottom: 30px;
}
.curriculum-cont .accordion {
    border: 0;
}
.curriculum-cont .accordion .card {
    border: 0;
    border-radius: 0;
    border-bottom: 1px solid #cecece !important;
}
.curriculum-cont .accordion .card:last-child {
    border-bottom: 0 !important;
}
.curriculum-cont .accordion .card .card-header {
    padding: 0;
    border-bottom: 0;
}
.curriculum-cont .accordion .card .card-header a {
    overflow: hidden;
    display: block;
    padding: 15px 15px;
    background-color: #edf0f2;
}
.curriculum-cont .accordion .card .card-header a.collapsed {
    background-color: #fff;
    border: none;
}
.curriculum-cont .accordion .card .card-header a::before, .curriculum-cont .accordion .card .card-header a.collapsed::before {
    content: "\f107";
    font-family: FontAwesome;
    font-size: 18px;
    color: #8a8a8a;
    position: absolute;
    top: 15px;
    right: 15px;
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
    -webkit-transition: all 0.2s linear;
    transition: all 0.2s linear;
}
.curriculum-cont .accordion .card .card-header a.collapsed:before {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
}
.curriculum-cont .accordion .card .card-header a ul li {
    display: inline-block;
}
.curriculum-cont .accordion .card .card-header a ul li:last-child {
    float: right;
}
.curriculum-cont .accordion .card .card-header a ul li>i {
    color: #082e55;
    font-size: 15px;
    margin-right: 5px;
}
.curriculum-cont .accordion .card .card-header a ul li>.lecture {
    font-size: 15px;
    color: #8a8a8a;
}
.curriculum-cont .accordion .card .card-header a ul li>.head {
    font-size: 16px;
    font-weight: 600;
    font-family: 'Montserrat', sans-serif;
    margin-left: 15px;
    color: #000;
}
.curriculum-cont .accordion .card .card-header a ul li>.time {
    font-size: 15px;
    color: #8a8a8a;
    text-align: right;
    padding-right: 30px;
}
.curriculum-cont .accordion .card .card-header a ul li>.time i {
    margin-right: 5px;
}
.curriculum-cont .accordion .card .card-header a ul li>.time span {}
.curriculum-cont .accordion .card .card-body {
    background-color: #edf0f2;
    padding: 0 25px 20px;
}
.instructor-cont {
    padding: 30px 30px 25px;
}
.instructor-cont .instructor-author {
    overflow: hidden;
}
.instructor-cont .instructor-author .author-thum {
    float: left;
    margin-right: 30px;
}
.instructor-cont .instructor-author .author-name {
    float: left;
}
.instructor-cont .instructor-author .author-name a {
    display: block;
}
.instructor-cont .instructor-author .author-name a h5 {
    font-size: 18px;
    color: 30px;
    font-weight: 600;
}
.instructor-cont .instructor-author .author-name span {
    font-size: 15px;
    color: #8a8a8a;
}
.instructor-cont .instructor-author .author-name .social {
    padding-top: 25px;
}
.instructor-cont .instructor-author .author-name .social li {
    display: inline-block;
    margin-right: 8px;
}
.instructor-cont .instructor-author .author-name .social li a {
    font-size: 16px;
    width: 35px;
    height: 35px;
    line-height: 33px;
    border: 1px solid #24486e;
    background-color: #24486e;
    color: #fff;
    text-align: center;
    -webkit-transition: all 0.4s linear;
    transition: all 0.4s linear;
}
.instructor-cont .instructor-author .author-name .social li a:hover {
    background-color: #fff;
    color: #24486e;
}
.reviews-cont {
    padding: 25px 30px 30px;
}
.reviews-cont .title h6 {
    font-size: 18px;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    color: #000;
}
.reviews-cont ul li .single-reviews {
    padding-top: 30px;
    padding-bottom: 25px;
    border-bottom: 1px solid #d2d2d2;
}
.reviews-cont ul li:last-child .single-reviews {
    border-bottom: 0;
}
.reviews-cont ul li .single-reviews .reviews-author {
    display: -moz-flex;
    display: -ms-flex;
    display: -o-flex;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.reviews-cont ul li .single-reviews .reviews-author .author-thum img {
    border-radius: 5px;
}
.reviews-cont ul li .single-reviews .reviews-author .author-name {
    padding-left: 20px;
}
.reviews-cont ul li .single-reviews .reviews-author .author-name h6 {
    font-size: 16px;
    font-weight: 600;
}
.reviews-cont ul li .single-reviews .reviews-author .author-name span {
    font-size: 15px;
    color: #8a8a8a;
    font-weight: 400;
    font-family: 'Poppins', sans-serif;
}
.reviews-cont ul li .single-reviews .reviews-description {}
.reviews-cont ul li .single-reviews .reviews-description p {
    padding-bottom: 17px;
}
.reviews-cont ul li .single-reviews .reviews-description .rating {
    display: -moz-flex;
    display: -ms-flex;
    display: -o-flex;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}
.reviews-cont ul li .single-reviews .reviews-description .rating ul li {
    display: inline-block;
    font-size: 15px;
    color: #082e55;
}
.reviews-cont ul li .single-reviews .reviews-description .rating span {
    font-size: 15px;
    color: #000;
    margin-left: 10px;
}
.reviews-cont .reviews-form .form-single {
    padding-top: 25px;
}
.reviews-cont .reviews-form .form-single input, .reviews-cont .reviews-form .form-single textarea {
    width: 100%;
    height: 45px;
    border: 1px solid #cecece;
    border-radius: 5px;
    padding: 0 20px;
}
.reviews-cont .reviews-form .form-single textarea {
    height: 160px;
    padding-top: 20px;
    resize: none;
}
.reviews-cont .reviews-form .form-single .rate-wrapper {
    overflow: hidden;
}
.reviews-cont .reviews-form .form-single .rate-label {
    float: left;
    color: #000;
    margin-right: 10px;
    margin-left: 0;
}
.reviews-cont .reviews-form .form-single .rate {
    float: left;
    color: #cecece;
    cursor: pointer;
}
.reviews-cont .reviews-form .form-single .rate-item {
    float: left;
    cursor: pointer;
    margin: 0px 3px 0px 3px;
}
.reviews-cont .reviews-form .form-single .rate:hover, .reviews-cont .reviews-form .form-single .rate.selected {
    color: #082e55;
}
.reviews-cont .reviews-form .form-single .rate .rate-item:hover~.rate-item, .reviews-cont .reviews-form .form-single .rate .rate-item.active~.rate-item {
    color: #cecece;
}
.related-courses .title h3 {
    font-size: 30px;
    color: #792326;
}
.course-features {
    background-color: #fff;
    padding: 30px 35px;
    border-radius: 5px;
}
.course-features h4, .You-makelike h4 {
    font-size: 24px;
    color: #792326;
    padding-bottom: 15px;
}
.course-features ul {}
.course-features ul li {
    font-size: 15px;
    color: #8a8a8a;
    overflow: hidden;
    line-height: 45px;
    border-bottom: 1px solid #d2d2d2;
}
.course-features ul li:last-child {
    border-bottom: 0;
}
.course-features ul li i {
    color: #082e55;
    margin-right: 8px;
}
.course-features ul li span {
    float: right;
}
.course-features .price-button {
    overflow: hidden;
}
.course-features .price-button span {
    font-size: 18px;
    color: #24486e;
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    margin-top: 12px;
}
.course-features .price-button span b {
    color: #082e55;
}
.course-features .price-button .main-btn {
    float: right;
}
.You-makelike {
    background-color: #fff;
    padding: 25px 30px 30px;
    border-radius: 5px;
}
.You-makelike .single-makelike {
    position: relative;
    border-radius: 5px;
    overflow: hidden;
}
.You-makelike .single-makelike .image {
    position: relative;
}
.You-makelike .single-makelike .image::before {
    position: absolute;
    content: '';
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-color: rgba(7, 41, 77, 0.8);
}
.You-makelike .single-makelike .image img {
    width: 100%;
}
.You-makelike .single-makelike .cont {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    padding: 0 30px;
}
.You-makelike .single-makelike .cont h4 {
    font-size: 18px;
    color: #fff;
    font-weight: 600;
    -webkit-transition: all 0.4s linear;
    transition: all 0.4s linear;
}
.You-makelike .single-makelike .cont h4:hover {
    color: #082e55;
}
.You-makelike .single-makelike .cont ul li {
    display: inline-block;
    margin-right: 15px;
    color: #082e55;
}
.You-makelike .single-makelike .cont ul li a {
    font-size: 14px;
    color: #fff;
}
.You-makelike .single-makelike .cont ul li a i {
    margin-right: 3px;
}
/*=====================================================
    24. EVENT PAGE
======================================================*/

.single-event-list {
    background-color: #fff;
    padding: 30px;
    border-radius: 5px;
    display: -moz-flex;
    display: -ms-flex;
    display: -o-flex;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-align-items: flex-start;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
}
.single-event-list .event-thum {
    width: 38%;
}
.single-event-list .event-thum img {
    width: 100%;
    border-radius: 5px;
}
.single-event-list .event-cont {
    width: 62%;
    padding-left: 30px;
}
.single-event-list .event-cont span {
    font-size: 14px;
    color: #8a8a8a;
    margin-right: 20px;
}
.single-event-list .event-cont span i {
    color: #082e55;
    margin-right: 3px;
}
.single-event-list .event-cont a {
    display: block;
}
.single-event-list .event-cont a h4 {
    padding-top: 5px;
    padding-bottom: 10px;
    color: #000;
    font-size: 20px;
    -webkit-transition: all 0.4s linear;
    transition: all 0.4s linear;
}
.single-event-list .event-cont a:hover h4 {
    color: #082e55;
}
/*=====================================================
    25. EVENT SINGEL PAGE css
======================================================*/

.events-area {
    background-color: #fff;
    padding: 45px 50px;
}
.events-left {}
.events-left h3 {
    padding-top: 5px;
    padding-bottom: 10px;
    color: #000;
    font-size: 30px;
}
.events-left span {
    font-size: 14px;
    color: #8a8a8a;
    margin-right: 20px;
}
.events-left span i {
    color: #082e55;
    margin-right: 3px;
}
.events-left img {
    margin-top: 35px;
    border-radius: 5px;
}
.events-left p {
    padding-top: 31px;
}
.events-right {}
.events-coundwon {
    padding: 25px 30px 30px;
    border-radius: 5px;
    overflow: hidden;
    margin-top: 110px;
}
.events-coundwon .count-down-time {
    position: relative;
    z-index: 5;
}
.events-coundwon .count-down-time .single-count {}
.events-coundwon .count-down-time .single-count .number {
    font-size: 24px;
    color: #fff;
}
.events-coundwon .count-down-time .single-count .title {
    font-size: 13px;
}
.events-coundwon .events-coundwon-btn {}
.events-coundwon .events-coundwon-btn .main-btn {
    width: 100%;
}
.events-address {
    border: 1px solid #bcbcbc;
    padding: 0 30px 30px;
    border-radius: 5px;
    height: 75px;
}
.events-address ul li {
    padding-top: 20px;
}
.events-address ul li .single-address {
    position: relative;
}
.events-address ul li .single-address .icon {
    position: absolute;
    top: 0;
    left: 0;
}
.events-address ul li .single-address .icon i {
    font-size: 15px;
    color: #082e55;
}
.events-address ul li .single-address .cont {
    padding-left: 23px;
}
.events-address ul li .single-address .cont h6 {
    font-size: 15px;
    color: #792326;
}
.events-address ul li .single-address .cont span {
    font-size: 14px;
    color: #505050;
}
#contact-map {
    width: 100%;
    height: 150px;
    border-radius: 5px;
}

/*=====================================================
    32. ACCORDION
======================================================*/

.faq-accordion-cont .title h6 {
    font-size: 18px;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    color: #000;
    padding-bottom: 30px;
}
.faq-accordion-cont .accordion {
    border: 0;
}
.faq-accordion-cont .accordion .card {
    border: 0;
    border-radius: 0;
    border-bottom: 1px solid #cecece !important;
}
.faq-accordion-cont .accordion .card:last-child {
    border-bottom: 0 !important;
}
.faq-accordion-cont .accordion .card .card-header {
    padding: 0;
    border-bottom: 0;
}
.faq-accordion-cont .accordion .card .card-header a {
    overflow: hidden;
    display: block;
    padding: 17px 25px;
    background-color: #792326;
}
.faq-accordion-cont .accordion .card .card-header a.collapsed {
    background-color: #fff;
    border: none;
}
.faq-accordion-cont .accordion .card .card-header a::before, .faq-accordion-cont .accordion .card .card-header a.collapsed::before {
    content: "\f107";
    font-family: FontAwesome;
    font-size: 22px;
    color: #fff;
    position: absolute;
    top: 15px;
    right: 25px;
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
    -webkit-transition: all 0.2s linear;
    transition: all 0.2s linear;
}
.faq-accordion-cont .accordion .card .card-header a.collapsed:before {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
    color: #8a8a8a;
}
.faq-accordion-cont .accordion .card .card-header a ul li {
    display: inline-block;
}
.faq-accordion-cont .accordion .card .card-header a ul li:last-child {
    float: right;
}
.faq-accordion-cont .accordion .card .card-header a ul li>i {
    color: #792326;
    font-size: 16px;
    margin-right: 5px;
}
.faq-accordion-cont .accordion .card .card-header a ul li>.lecture {
    font-size: 15px;
    color: #8a8a8a;
}
.faq-accordion-cont .accordion .card .card-header a ul li>.head {
    font-size: 16px;
    font-weight: 600;
    font-family: 'Montserrat', sans-serif;
    margin-left: 15px;
    color: #fff;
}
.faq-accordion-cont .accordion .card .card-header a.collapsed ul li>.head {
    color: #792326;
}
.faq-accordion-cont .accordion .card .card-header a ul li>.time {
    font-size: 15px;
    color: #8a8a8a;
    text-align: right;
    padding-right: 30px;
}
.faq-accordion-cont .accordion .card .card-header a ul li>.time i {
    margin-right: 5px;
}
.faq-accordion-cont .accordion .card .card-body {
    padding: 30px;
}
