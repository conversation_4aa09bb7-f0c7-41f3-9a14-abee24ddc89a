.navbar-custom {
    background-color: #f5f5f5;
    /*background: #092E56;*/
    color: #092E56;

}
.nav-second-level li{
    font-size: 1.0em;
    margin-left: -20px;
    color: #ffffff;
}
.nav-second-level li >a{
    font-size: 1.0em;
    color: #ffffff;
}
.nav-second-level li >a.active{
    font-size: 1.0em;
    color: #CC2629;
}
.nav-second-level li >a:hover{
    font-size: 1.0em;
    color: #CC2629;
}
.navbar-custom .topnav-menu .nav-link {
    color: #0C2C5C;
    font-weight: bold;
}
.navbar-custom .dropdown.show .nav-link {
    background-color: rgba(255,255,255,.1);
    color:#1a1e24;
}
.navbar-custom .button-menu-mobile {
    color: #1a1e24;
}
.navbar-custom .app-search .btn {
    color: #ffffff;
    background: #CC2629;
}
.navbar-custom .app-search .form-control {
    color: #ffffff;
    background:#bdbdbd;
}

.logo-box{
    background: #f5f5f5;
    color:#092E56;
}
.logo .logo-lg-text-light {
    color: #F58320;
}
.left-side-menu {
    background: #424242;
}
#sidebar-menu>ul>li>a {
    color: #f2f2f2;

}
#sidebar-menu>ul>li>a:hover {
    color: #F58320;
    font-weight: bold;

}
#sidebar-menu>ul>li>a.active {
    color: #f2f2f2;
    font-weight: bold;
    background-color: #CC2629;
}
.breadcrumb-item.active {
    color: #F58320;
}
.card-header {
    background: #CC2629;
}
.breadcrumb-item>a {
    color: #CC2629;
}
.metismenu li a[aria-expanded=true] {
    color: #f2f2f2!important;
}
.enlarged .left-side-menu #sidebar-menu>ul>li:hover>a {
    color: #f2f2f2;
    background-color: #CC2629;
}
.nav-pills>li>a, .nav-tabs>li>a {
    color: #F58320;
}
