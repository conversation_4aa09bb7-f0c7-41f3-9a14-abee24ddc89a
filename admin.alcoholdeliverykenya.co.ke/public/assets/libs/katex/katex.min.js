!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.katex=e():t.katex=e()}("undefined"!=typeof self?self:this,function(){return function(t){var e={};function r(n){if(e[n])return e[n].exports;var a=e[n]={i:n,l:!1,exports:{}};return t[n].call(a.exports,a,a.exports,r),a.l=!0,a.exports}return r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var a in t)r.d(n,a,function(e){return t[e]}.bind(null,a));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=1)}([function(t,e,r){},function(t,e,r){"use strict";r.r(e);r(0);var n=function(){function t(t,e,r){this.lexer=void 0,this.start=void 0,this.end=void 0,this.lexer=t,this.start=e,this.end=r}return t.range=function(e,r){return r?e&&e.loc&&r.loc&&e.loc.lexer===r.loc.lexer?new t(e.loc.lexer,e.loc.start,r.loc.end):null:e&&e.loc},t}(),a=function(){function t(t,e){this.text=void 0,this.loc=void 0,this.text=t,this.loc=e}return t.prototype.range=function(e,r){return new t(r,n.range(this,e))},t}(),i=function t(e,r){this.position=void 0;var n,a="KaTeX parse error: "+e,i=r&&r.loc;if(i&&i.start<=i.end){var o=i.lexer.input;n=i.start;var s=i.end;n===o.length?a+=" at end of input: ":a+=" at position "+(n+1)+": ";var h=o.slice(n,s).replace(/[^]/g,"$&\u0332");a+=(n>15?"\u2026"+o.slice(n-15,n):o.slice(0,n))+h+(s+15<o.length?o.slice(s,s+15)+"\u2026":o.slice(s))}var l=new Error(a);return l.name="ParseError",l.__proto__=t.prototype,l.position=n,l};i.prototype.__proto__=Error.prototype;var o=i,s=/([A-Z])/g,h={"&":"&amp;",">":"&gt;","<":"&lt;",'"':"&quot;","'":"&#x27;"},l=/[&><"']/g;var m=function t(e){return"ordgroup"===e.type?1===e.body.length?t(e.body[0]):e:"color"===e.type?1===e.body.length?t(e.body[0]):e:"font"===e.type?t(e.body):e},c={contains:function(t,e){return-1!==t.indexOf(e)},deflt:function(t,e){return void 0===t?e:t},escape:function(t){return String(t).replace(l,function(t){return h[t]})},hyphenate:function(t){return t.replace(s,"-$1").toLowerCase()},getBaseElem:m,isCharacterBox:function(t){var e=m(t);return"mathord"===e.type||"textord"===e.type||"atom"===e.type}},u=function(){function t(t){this.displayMode=void 0,this.leqno=void 0,this.fleqn=void 0,this.throwOnError=void 0,this.errorColor=void 0,this.macros=void 0,this.colorIsTextColor=void 0,this.strict=void 0,this.maxSize=void 0,this.maxExpand=void 0,this.allowedProtocols=void 0,t=t||{},this.displayMode=c.deflt(t.displayMode,!1),this.leqno=c.deflt(t.leqno,!1),this.fleqn=c.deflt(t.fleqn,!1),this.throwOnError=c.deflt(t.throwOnError,!0),this.errorColor=c.deflt(t.errorColor,"#cc0000"),this.macros=t.macros||{},this.colorIsTextColor=c.deflt(t.colorIsTextColor,!1),this.strict=c.deflt(t.strict,"warn"),this.maxSize=Math.max(0,c.deflt(t.maxSize,1/0)),this.maxExpand=Math.max(0,c.deflt(t.maxExpand,1e3)),this.allowedProtocols=c.deflt(t.allowedProtocols,["http","https","mailto","_relative"])}var e=t.prototype;return e.reportNonstrict=function(t,e,r){var n=this.strict;if("function"==typeof n&&(n=n(t,e,r)),n&&"ignore"!==n){if(!0===n||"error"===n)throw new o("LaTeX-incompatible input and strict mode is set to 'error': "+e+" ["+t+"]",r);"warn"===n?"undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+e+" ["+t+"]"):"undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to unrecognized '"+n+"': "+e+" ["+t+"]")}},e.useStrictBehavior=function(t,e,r){var n=this.strict;if("function"==typeof n)try{n=n(t,e,r)}catch(t){n="error"}return!(!n||"ignore"===n)&&(!0===n||"error"===n||("warn"===n?("undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+e+" ["+t+"]"),!1):("undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to unrecognized '"+n+"': "+e+" ["+t+"]"),!1)))},t}(),d=function(){function t(t,e,r){this.id=void 0,this.size=void 0,this.cramped=void 0,this.id=t,this.size=e,this.cramped=r}var e=t.prototype;return e.sup=function(){return p[f[this.id]]},e.sub=function(){return p[g[this.id]]},e.fracNum=function(){return p[x[this.id]]},e.fracDen=function(){return p[v[this.id]]},e.cramp=function(){return p[y[this.id]]},e.text=function(){return p[b[this.id]]},e.isTight=function(){return this.size>=2},t}(),p=[new d(0,0,!1),new d(1,0,!0),new d(2,1,!1),new d(3,1,!0),new d(4,2,!1),new d(5,2,!0),new d(6,3,!1),new d(7,3,!0)],f=[4,5,4,5,6,7,6,7],g=[5,5,5,5,7,7,7,7],x=[2,3,4,5,6,7,6,7],v=[3,3,5,5,7,7,7,7],y=[1,1,3,3,5,5,7,7],b=[0,1,2,3,2,3,2,3],w={DISPLAY:p[0],TEXT:p[2],SCRIPT:p[4],SCRIPTSCRIPT:p[6]},k=[{name:"latin",blocks:[[256,591],[768,879]]},{name:"cyrillic",blocks:[[1024,1279]]},{name:"brahmic",blocks:[[2304,4255]]},{name:"georgian",blocks:[[4256,4351]]},{name:"cjk",blocks:[[12288,12543],[19968,40879],[65280,65376]]},{name:"hangul",blocks:[[44032,55215]]}];var S=[];function z(t){for(var e=0;e<S.length;e+=2)if(t>=S[e]&&t<=S[e+1])return!0;return!1}k.forEach(function(t){return t.blocks.forEach(function(t){return S.push.apply(S,t)})});var M={path:{sqrtMain:"M95,702c-2.7,0,-7.17,-2.7,-13.5,-8c-5.8,-5.3,-9.5,\n-10,-9.5,-14c0,-2,0.3,-3.3,1,-4c1.3,-2.7,23.83,-20.7,67.5,-54c44.2,-33.3,65.8,\n-50.3,66.5,-51c1.3,-1.3,3,-2,5,-2c4.7,0,8.7,3.3,12,10s173,378,173,378c0.7,0,\n35.3,-71,104,-213c68.7,-142,137.5,-285,206.5,-429c69,-144,104.5,-217.7,106.5,\n-221c5.3,-9.3,12,-14,20,-14H400000v40H845.2724s-225.272,467,-225.272,467\ns-235,486,-235,486c-2.7,4.7,-9,7,-19,7c-6,0,-10,-1,-12,-3s-194,-422,-194,-422\ns-65,47,-65,47z M834 80H400000v40H845z",sqrtSize1:"M263,681c0.7,0,18,39.7,52,119c34,79.3,68.167,\n158.7,102.5,238c34.3,79.3,51.8,119.3,52.5,120c340,-704.7,510.7,-1060.3,512,-1067\nc4.7,-7.3,11,-11,19,-11H40000v40H1012.3s-271.3,567,-271.3,567c-38.7,80.7,-84,\n175,-136,283c-52,108,-89.167,185.3,-111.5,232c-22.3,46.7,-33.8,70.3,-34.5,71\nc-4.7,4.7,-12.3,7,-23,7s-12,-1,-12,-1s-109,-253,-109,-253c-72.7,-168,-109.3,\n-252,-110,-252c-10.7,8,-22,16.7,-34,26c-22,17.3,-33.3,26,-34,26s-26,-26,-26,-26\ns76,-59,76,-59s76,-60,76,-60z M1001 80H40000v40H1012z",sqrtSize2:"M1001,80H400000v40H1013.1s-83.4,268,-264.1,840c-180.7,\n572,-277,876.3,-289,913c-4.7,4.7,-12.7,7,-24,7s-12,0,-12,0c-1.3,-3.3,-3.7,-11.7,\n-7,-25c-35.3,-125.3,-106.7,-373.3,-214,-744c-10,12,-21,25,-33,39s-32,39,-32,39\nc-6,-5.3,-15,-14,-27,-26s25,-30,25,-30c26.7,-32.7,52,-63,76,-91s52,-60,52,-60\ns208,722,208,722c56,-175.3,126.3,-397.3,211,-666c84.7,-268.7,153.8,-488.2,207.5,\n-658.5c53.7,-170.3,84.5,-266.8,92.5,-289.5c4,-6.7,10,-10,18,-10z\nM1001 80H400000v40H1013z",sqrtSize3:"M424,2478c-1.3,-0.7,-38.5,-172,-111.5,-514c-73,\n-342,-109.8,-513.3,-110.5,-514c0,-2,-10.7,14.3,-32,49c-4.7,7.3,-9.8,15.7,-15.5,\n25c-5.7,9.3,-9.8,16,-12.5,20s-5,7,-5,7c-4,-3.3,-8.3,-7.7,-13,-13s-13,-13,-13,\n-13s76,-122,76,-122s77,-121,77,-121s209,968,209,968c0,-2,84.7,-361.7,254,-1079\nc169.3,-717.3,254.7,-1077.7,256,-1081c4,-6.7,10,-10,18,-10H400000v40H1014.6\ns-87.3,378.7,-272.6,1166c-185.3,787.3,-279.3,1182.3,-282,1185c-2,6,-10,9,-24,9\nc-8,0,-12,-0.7,-12,-2z M1001 80H400000v40H1014z",sqrtSize4:"M473,2793c339.3,-1799.3,509.3,-2700,510,-2702\nc3.3,-7.3,9.3,-11,18,-11H400000v40H1017.7s-90.5,478,-276.2,1466c-185.7,988,\n-279.5,1483,-281.5,1485c-2,6,-10,9,-24,9c-8,0,-12,-0.7,-12,-2c0,-1.3,-5.3,-32,\n-16,-92c-50.7,-293.3,-119.7,-693.3,-207,-1200c0,-1.3,-5.3,8.7,-16,30c-10.7,\n21.3,-21.3,42.7,-32,64s-16,33,-16,33s-26,-26,-26,-26s76,-153,76,-153s77,-151,\n77,-151c0.7,0.7,35.7,202,105,604c67.3,400.7,102,602.7,104,606z\nM1001 80H400000v40H1017z",doubleleftarrow:"M262 157\nl10-10c34-36 62.7-77 86-123 3.3-8 5-13.3 5-16 0-5.3-6.7-8-20-8-7.3\n 0-12.2.5-14.5 1.5-2.3 1-4.8 4.5-7.5 10.5-49.3 97.3-121.7 169.3-217 216-28\n 14-57.3 25-88 33-6.7 2-11 3.8-13 5.5-2 1.7-3 4.2-3 7.5s1 5.8 3 7.5\nc2 1.7 6.3 3.5 13 5.5 68 17.3 128.2 47.8 180.5 91.5 52.3 43.7 93.8 96.2 124.5\n 157.5 9.3 8 15.3 12.3 18 13h6c12-.7 18-4 18-10 0-2-1.7-7-5-15-23.3-46-52-87\n-86-123l-10-10h399738v-40H218c328 0 0 0 0 0l-10-8c-26.7-20-65.7-43-117-69 2.7\n-2 6-3.7 10-5 36.7-16 72.3-37.3 107-64l10-8h399782v-40z\nm8 0v40h399730v-40zm0 194v40h399730v-40z",doublerightarrow:"M399738 392l\n-10 10c-34 36-62.7 77-86 123-3.3 8-5 13.3-5 16 0 5.3 6.7 8 20 8 7.3 0 12.2-.5\n 14.5-1.5 2.3-1 4.8-4.5 7.5-10.5 49.3-97.3 121.7-169.3 217-216 28-14 57.3-25 88\n-33 6.7-2 11-3.8 13-5.5 2-1.7 3-4.2 3-7.5s-1-5.8-3-7.5c-2-1.7-6.3-3.5-13-5.5-68\n-17.3-128.2-47.8-180.5-91.5-52.3-43.7-93.8-96.2-124.5-157.5-9.3-8-15.3-12.3-18\n-13h-6c-12 .7-18 4-18 10 0 2 1.7 7 5 15 23.3 46 52 87 86 123l10 10H0v40h399782\nc-328 0 0 0 0 0l10 8c26.7 20 65.7 43 117 69-2.7 2-6 3.7-10 5-36.7 16-72.3 37.3\n-107 64l-10 8H0v40zM0 157v40h399730v-40zm0 194v40h399730v-40z",leftarrow:"M400000 241H110l3-3c68.7-52.7 113.7-120\n 135-202 4-14.7 6-23 6-25 0-7.3-7-11-21-11-8 0-13.2.8-15.5 2.5-2.3 1.7-4.2 5.8\n-5.5 12.5-1.3 4.7-2.7 10.3-4 17-12 48.7-34.8 92-68.5 130S65.3 228.3 18 247\nc-10 4-16 7.7-18 11 0 8.7 6 14.3 18 17 47.3 18.7 87.8 47 121.5 85S196 441.3 208\n 490c.7 2 1.3 5 2 9s1.2 6.7 1.5 8c.3 1.3 1 3.3 2 6s2.2 4.5 3.5 5.5c1.3 1 3.3\n 1.8 6 2.5s6 1 10 1c14 0 21-3.7 21-11 0-2-2-10.3-6-25-20-79.3-65-146.7-135-202\n l-3-3h399890zM100 241v40h399900v-40z",leftbrace:"M6 548l-6-6v-35l6-11c56-104 135.3-181.3 238-232 57.3-28.7 117\n-45 179-50h399577v120H403c-43.3 7-81 15-113 26-100.7 33-179.7 91-237 174-2.7\n 5-6 9-10 13-.7 1-7.3 1-20 1H6z",leftbraceunder:"M0 6l6-6h17c12.688 0 19.313.3 20 1 4 4 7.313 8.3 10 13\n 35.313 51.3 80.813 93.8 136.5 127.5 55.688 33.7 117.188 55.8 184.5 66.5.688\n 0 2 .3 4 1 18.688 2.7 76 4.3 172 5h399450v120H429l-6-1c-124.688-8-235-61.7\n-331-161C60.687 138.7 32.312 99.3 7 54L0 41V6z",leftgroup:"M400000 80\nH435C64 80 168.3 229.4 21 260c-5.9 1.2-18 0-18 0-2 0-3-1-3-3v-38C76 61 257 0\n 435 0h399565z",leftgroupunder:"M400000 262\nH435C64 262 168.3 112.6 21 82c-5.9-1.2-18 0-18 0-2 0-3 1-3 3v38c76 158 257 219\n 435 219h399565z",leftharpoon:"M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3\n-3.3 10.2-9.5 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5\n-18.3 3-21-1.3-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7\n-196 228-6.7 4.7-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40z",leftharpoonplus:"M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3-3.3 10.2-9.5\n 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5-18.3 3-21-1.3\n-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7-196 228-6.7 4.7\n-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40zM0 435v40h400000v-40z\nm0 0v40h400000v-40z",leftharpoondown:"M7 241c-4 4-6.333 8.667-7 14 0 5.333.667 9 2 11s5.333\n 5.333 12 10c90.667 54 156 130 196 228 3.333 10.667 6.333 16.333 9 17 2 .667 5\n 1 9 1h5c10.667 0 16.667-2 18-6 2-2.667 1-9.667-3-21-32-87.333-82.667-157.667\n-152-211l-3-3h399907v-40zM93 281 H400000 v-40L7 241z",leftharpoondownplus:"M7 435c-4 4-6.3 8.7-7 14 0 5.3.7 9 2 11s5.3 5.3 12\n 10c90.7 54 156 130 196 228 3.3 10.7 6.3 16.3 9 17 2 .7 5 1 9 1h5c10.7 0 16.7\n-2 18-6 2-2.7 1-9.7-3-21-32-87.3-82.7-157.7-152-211l-3-3h399907v-40H7zm93 0\nv40h399900v-40zM0 241v40h399900v-40zm0 0v40h399900v-40z",lefthook:"M400000 281 H103s-33-11.2-61-33.5S0 197.3 0 164s14.2-61.2 42.5\n-83.5C70.8 58.2 104 47 142 47 c16.7 0 25 6.7 25 20 0 12-8.7 18.7-26 20-40 3.3\n-68.7 15.7-86 37-10 12-15 25.3-15 40 0 22.7 9.8 40.7 29.5 54 19.7 13.3 43.5 21\n 71.5 23h399859zM103 281v-40h399897v40z",leftlinesegment:"M40 281 V428 H0 V94 H40 V241 H400000 v40z\nM40 281 V428 H0 V94 H40 V241 H400000 v40z",leftmapsto:"M40 281 V448H0V74H40V241H400000v40z\nM40 281 V448H0V74H40V241H400000v40z",leftToFrom:"M0 147h400000v40H0zm0 214c68 40 115.7 95.7 143 167h22c15.3 0 23\n-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69-70-101l-7-8h399905v-40H95l7-8\nc28.7-32 52-65.7 70-101 10.7-23.3 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 265.3\n 68 321 0 361zm0-174v-40h399900v40zm100 154v40h399900v-40z",longequal:"M0 50 h400000 v40H0z m0 194h40000v40H0z\nM0 50 h400000 v40H0z m0 194h40000v40H0z",midbrace:"M200428 334\nc-100.7-8.3-195.3-44-280-108-55.3-42-101.7-93-139-153l-9-14c-2.7 4-5.7 8.7-9 14\n-53.3 86.7-123.7 153-211 199-66.7 36-137.3 56.3-212 62H0V214h199568c178.3-11.7\n 311.7-78.3 403-201 6-8 9.7-12 11-12 .7-.7 6.7-1 18-1s17.3.3 18 1c1.3 0 5 4 11\n 12 44.7 59.3 101.3 106.3 170 141s145.3 54.3 229 60h199572v120z",midbraceunder:"M199572 214\nc100.7 8.3 195.3 44 280 108 55.3 42 101.7 93 139 153l9 14c2.7-4 5.7-8.7 9-14\n 53.3-86.7 123.7-153 211-199 66.7-36 137.3-56.3 212-62h199568v120H200432c-178.3\n 11.7-311.7 78.3-403 201-6 8-9.7 12-11 12-.7.7-6.7 1-18 1s-17.3-.3-18-1c-1.3 0\n-5-4-11-12-44.7-59.3-101.3-106.3-170-141s-145.3-54.3-229-60H0V214z",oiintSize1:"M512.6 71.6c272.6 0 320.3 106.8 320.3 178.2 0 70.8-47.7 177.6\n-320.3 177.6S193.1 320.6 193.1 249.8c0-71.4 46.9-178.2 319.5-178.2z\nm368.1 178.2c0-86.4-60.9-215.4-368.1-215.4-306.4 0-367.3 129-367.3 215.4 0 85.8\n60.9 214.8 367.3 214.8 307.2 0 368.1-129 368.1-214.8z",oiintSize2:"M757.8 100.1c384.7 0 451.1 137.6 451.1 230 0 91.3-66.4 228.8\n-451.1 228.8-386.3 0-452.7-137.5-452.7-228.8 0-92.4 66.4-230 452.7-230z\nm502.4 230c0-111.2-82.4-277.2-502.4-277.2s-504 166-504 277.2\nc0 110 84 276 504 276s502.4-166 502.4-276z",oiiintSize1:"M681.4 71.6c408.9 0 480.5 106.8 480.5 178.2 0 70.8-71.6 177.6\n-480.5 177.6S202.1 320.6 202.1 249.8c0-71.4 70.5-178.2 479.3-178.2z\nm525.8 178.2c0-86.4-86.8-215.4-525.7-215.4-437.9 0-524.7 129-524.7 215.4 0\n85.8 86.8 214.8 524.7 214.8 438.9 0 525.7-129 525.7-214.8z",oiiintSize2:"M1021.2 53c603.6 0 707.8 165.8 707.8 277.2 0 110-104.2 275.8\n-707.8 275.8-606 0-710.2-165.8-710.2-275.8C311 218.8 415.2 53 1021.2 53z\nm770.4 277.1c0-131.2-126.4-327.6-770.5-327.6S248.4 198.9 248.4 330.1\nc0 130 128.8 326.4 772.7 326.4s770.5-196.4 770.5-326.4z",rightarrow:"M0 241v40h399891c-47.3 35.3-84 78-110 128\n-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20\n 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7\n 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85\n-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n 151.7 139 205zm0 0v40h399900v-40z",rightbrace:"M400000 542l\n-6 6h-17c-12.7 0-19.3-.3-20-1-4-4-7.3-8.3-10-13-35.3-51.3-80.8-93.8-136.5-127.5\ns-117.2-55.8-184.5-66.5c-.7 0-2-.3-4-1-18.7-2.7-76-4.3-172-5H0V214h399571l6 1\nc124.7 8 235 61.7 331 161 31.3 33.3 59.7 72.7 85 118l7 13v35z",rightbraceunder:"M399994 0l6 6v35l-6 11c-56 104-135.3 181.3-238 232-57.3\n 28.7-117 45-179 50H-300V214h399897c43.3-7 81-15 113-26 100.7-33 179.7-91 237\n-174 2.7-5 6-9 10-13 .7-1 7.3-1 20-1h17z",rightgroup:"M0 80h399565c371 0 266.7 149.4 414 180 5.9 1.2 18 0 18 0 2 0\n 3-1 3-3v-38c-76-158-257-219-435-219H0z",rightgroupunder:"M0 262h399565c371 0 266.7-149.4 414-180 5.9-1.2 18 0 18\n 0 2 0 3 1 3 3v38c-76 158-257 219-435 219H0z",rightharpoon:"M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3\n-3.7-15.3-11-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2\n-10.7 0-16.7 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58\n 69.2 92 94.5zm0 0v40h399900v-40z",rightharpoonplus:"M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3-3.7-15.3-11\n-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2-10.7 0-16.7\n 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58 69.2 92 94.5z\nm0 0v40h399900v-40z m100 194v40h399900v-40zm0 0v40h399900v-40z",rightharpoondown:"M399747 511c0 7.3 6.7 11 20 11 8 0 13-.8 15-2.5s4.7-6.8\n 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3 8.5-5.8 9.5\n-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3-64.7 57-92 95\n-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 241v40h399900v-40z",rightharpoondownplus:"M399747 705c0 7.3 6.7 11 20 11 8 0 13-.8\n 15-2.5s4.7-6.8 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3\n 8.5-5.8 9.5-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3\n-64.7 57-92 95-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 435v40h399900v-40z\nm0-194v40h400000v-40zm0 0v40h400000v-40z",righthook:"M399859 241c-764 0 0 0 0 0 40-3.3 68.7-15.7 86-37 10-12 15-25.3\n 15-40 0-22.7-9.8-40.7-29.5-54-19.7-13.3-43.5-21-71.5-23-17.3-1.3-26-8-26-20 0\n-13.3 8.7-20 26-20 38 0 71 11.2 99 33.5 0 0 7 5.6 21 16.7 14 11.2 21 33.5 21\n 66.8s-14 61.2-42 83.5c-28 22.3-61 33.5-99 33.5L0 241z M0 281v-40h399859v40z",rightlinesegment:"M399960 241 V94 h40 V428 h-40 V281 H0 v-40z\nM399960 241 V94 h40 V428 h-40 V281 H0 v-40z",rightToFrom:"M400000 167c-70.7-42-118-97.7-142-167h-23c-15.3 0-23 .3-23\n 1 0 1.3 5.3 13.7 16 37 18 35.3 41.3 69 70 101l7 8H0v40h399905l-7 8c-28.7 32\n-52 65.7-70 101-10.7 23.3-16 35.7-16 37 0 .7 7.7 1 23 1h23c24-69.3 71.3-125 142\n-167z M100 147v40h399900v-40zM0 341v40h399900v-40z",twoheadleftarrow:"M0 167c68 40\n 115.7 95.7 143 167h22c15.3 0 23-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69\n-70-101l-7-8h125l9 7c50.7 39.3 85 86 103 140h46c0-4.7-6.3-18.7-19-42-18-35.3\n-40-67.3-66-96l-9-9h399716v-40H284l9-9c26-28.7 48-60.7 66-96 12.7-23.333 19\n-37.333 19-42h-46c-18 54-52.3 100.7-103 140l-9 7H95l7-8c28.7-32 52-65.7 70-101\n 10.7-23.333 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 71.3 68 127 0 167z",twoheadrightarrow:"M400000 167\nc-68-40-115.7-95.7-143-167h-22c-15.3 0-23 .3-23 1 0 1.3 5.3 13.7 16 37 18 35.3\n 41.3 69 70 101l7 8h-125l-9-7c-50.7-39.3-85-86-103-140h-46c0 4.7 6.3 18.7 19 42\n 18 35.3 40 67.3 66 96l9 9H0v40h399716l-9 9c-26 28.7-48 60.7-66 96-12.7 23.333\n-19 37.333-19 42h46c18-54 52.3-100.7 103-140l9-7h125l-7 8c-28.7 32-52 65.7-70\n 101-10.7 23.333-16 35.7-16 37 0 .7 7.7 1 23 1h22c27.3-71.3 75-127 143-167z",tilde1:"M200 55.538c-77 0-168 73.953-177 73.953-3 0-7\n-2.175-9-5.437L2 97c-1-2-2-4-2-6 0-4 2-7 5-9l20-12C116 12 171 0 207 0c86 0\n 114 68 191 68 78 0 168-68 177-68 4 0 7 2 9 5l12 19c1 2.175 2 4.35 2 6.525 0\n 4.35-2 7.613-5 9.788l-19 13.05c-92 63.077-116.937 75.308-183 76.128\n-68.267.847-113-73.952-191-73.952z",tilde2:"M344 55.266c-142 0-300.638 81.316-311.5 86.418\n-8.01 3.762-22.5 10.91-23.5 5.562L1 120c-1-2-1-3-1-4 0-5 3-9 8-10l18.4-9C160.9\n 31.9 283 0 358 0c148 0 188 122 331 122s314-97 326-97c4 0 8 2 10 7l7 21.114\nc1 2.14 1 3.21 1 4.28 0 5.347-3 9.626-7 10.696l-22.3 12.622C852.6 158.372 751\n 181.476 676 181.476c-149 0-189-126.21-332-126.21z",tilde3:"M786 59C457 59 32 175.242 13 175.242c-6 0-10-3.457\n-11-10.37L.15 138c-1-7 3-12 10-13l19.2-6.4C378.4 40.7 634.3 0 804.3 0c337 0\n 411.8 157 746.8 157 328 0 754-112 773-112 5 0 10 3 11 9l1 14.075c1 8.066-.697\n 16.595-6.697 17.492l-21.052 7.31c-367.9 98.146-609.15 122.696-778.15 122.696\n -338 0-409-156.573-744-156.573z",tilde4:"M786 58C457 58 32 177.487 13 177.487c-6 0-10-3.345\n-11-10.035L.15 143c-1-7 3-12 10-13l22-6.7C381.2 35 637.15 0 807.15 0c337 0 409\n 177 744 177 328 0 754-127 773-127 5 0 10 3 11 9l1 14.794c1 7.805-3 13.38-9\n 14.495l-20.7 5.574c-366.85 99.79-607.3 139.372-776.3 139.372-338 0-409\n -175.236-744-175.236z",vec:"M377 20c0-5.333 1.833-10 5.5-14S391 0 397 0c4.667 0 8.667 1.667 12 5\n3.333 2.667 6.667 9 10 19 6.667 24.667 20.333 43.667 41 57 7.333 4.667 11\n10.667 11 18 0 6-1 10-3 12s-6.667 5-14 9c-28.667 14.667-53.667 35.667-75 63\n-1.333 1.333-3.167 3.5-5.5 6.5s-4 4.833-5 5.5c-1 .667-2.5 1.333-4.5 2s-4.333 1\n-7 1c-4.667 0-9.167-1.833-13.5-5.5S337 184 337 178c0-12.667 15.667-32.333 47-59\nH213l-171-1c-8.667-6-13-12.333-13-19 0-4.667 4.333-11.333 13-20h359\nc-16-25.333-24-45-24-59z",widehat1:"M529 0h5l519 115c5 1 9 5 9 10 0 1-1 2-1 3l-4 22\nc-1 5-5 9-11 9h-2L532 67 19 159h-2c-5 0-9-4-11-9l-5-22c-1-6 2-12 8-13z",widehat2:"M1181 0h2l1171 176c6 0 10 5 10 11l-2 23c-1 6-5 10\n-11 10h-1L1182 67 15 220h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z",widehat3:"M1181 0h2l1171 236c6 0 10 5 10 11l-2 23c-1 6-5 10\n-11 10h-1L1182 67 15 280h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z",widehat4:"M1181 0h2l1171 296c6 0 10 5 10 11l-2 23c-1 6-5 10\n-11 10h-1L1182 67 15 340h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z",widecheck1:"M529,159h5l519,-115c5,-1,9,-5,9,-10c0,-1,-1,-2,-1,-3l-4,-22c-1,\n-5,-5,-9,-11,-9h-2l-512,92l-513,-92h-2c-5,0,-9,4,-11,9l-5,22c-1,6,2,12,8,13z",widecheck2:"M1181,220h2l1171,-176c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,\n-11,-10h-1l-1168,153l-1167,-153h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z",widecheck3:"M1181,280h2l1171,-236c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,\n-11,-10h-1l-1168,213l-1167,-213h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z",widecheck4:"M1181,340h2l1171,-296c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,\n-11,-10h-1l-1168,273l-1167,-273h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z",baraboveleftarrow:"M400000 620h-399890l3 -3c68.7 -52.7 113.7 -120 135 -202\nc4 -14.7 6 -23 6 -25c0 -7.3 -7 -11 -21 -11c-8 0 -13.2 0.8 -15.5 2.5\nc-2.3 1.7 -4.2 5.8 -5.5 12.5c-1.3 4.7 -2.7 10.3 -4 17c-12 48.7 -34.8 92 -68.5 130\ns-74.2 66.3 -121.5 85c-10 4 -16 7.7 -18 11c0 8.7 6 14.3 18 17c47.3 18.7 87.8 47\n121.5 85s56.5 81.3 68.5 130c0.7 2 1.3 5 2 9s1.2 6.7 1.5 8c0.3 1.3 1 3.3 2 6\ns2.2 4.5 3.5 5.5c1.3 1 3.3 1.8 6 2.5s6 1 10 1c14 0 21 -3.7 21 -11\nc0 -2 -2 -10.3 -6 -25c-20 -79.3 -65 -146.7 -135 -202l-3 -3h399890z\nM100 620v40h399900v-40z M0 241v40h399900v-40zM0 241v40h399900v-40z",rightarrowabovebar:"M0 241v40h399891c-47.3 35.3-84 78-110 128-16.7 32\n-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20 11 8 0\n13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7 39\n-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85-40.5\n-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n151.7 139 205zm96 379h399894v40H0zm0 0h399904v40H0z",baraboveshortleftharpoon:"M507,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11\nc1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17\nc2,0.7,5,1,9,1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21\nc-32,-87.3,-82.7,-157.7,-152,-211c0,0,-3,-3,-3,-3l399351,0l0,-40\nc-398570,0,-399437,0,-399437,0z M593 435 v40 H399500 v-40z\nM0 281 v-40 H399908 v40z M0 281 v-40 H399908 v40z",rightharpoonaboveshortbar:"M0,241 l0,40c399126,0,399993,0,399993,0\nc4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,\n-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6\nc-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z\nM0 241 v40 H399908 v-40z M0 475 v-40 H399500 v40z M0 475 v-40 H399500 v40z",shortbaraboveleftharpoon:"M7,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11\nc1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17c2,0.7,5,1,9,\n1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21c-32,-87.3,-82.7,-157.7,\n-152,-211c0,0,-3,-3,-3,-3l399907,0l0,-40c-399126,0,-399993,0,-399993,0z\nM93 435 v40 H400000 v-40z M500 241 v40 H400000 v-40z M500 241 v40 H400000 v-40z",shortrightharpoonabovebar:"M53,241l0,40c398570,0,399437,0,399437,0\nc4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,\n-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6\nc-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z\nM500 241 v40 H399408 v-40z M500 435 v40 H400000 v-40z"}},T=function(){function t(t){this.children=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.children=t,this.classes=[],this.height=0,this.depth=0,this.maxFontSize=0,this.style={}}var e=t.prototype;return e.hasClass=function(t){return c.contains(this.classes,t)},e.toNode=function(){for(var t=document.createDocumentFragment(),e=0;e<this.children.length;e++)t.appendChild(this.children[e].toNode());return t},e.toMarkup=function(){for(var t="",e=0;e<this.children.length;e++)t+=this.children[e].toMarkup();return t},e.toText=function(){var t=function(t){return t.toText()};return this.children.map(t).join("")},t}(),A=function(t){return t.filter(function(t){return t}).join(" ")},B=function(t,e,r){if(this.classes=t||[],this.attributes={},this.height=0,this.depth=0,this.maxFontSize=0,this.style=r||{},e){e.style.isTight()&&this.classes.push("mtight");var n=e.getColor();n&&(this.style.color=n)}},C=function(t){var e=document.createElement(t);for(var r in e.className=A(this.classes),this.style)this.style.hasOwnProperty(r)&&(e.style[r]=this.style[r]);for(var n in this.attributes)this.attributes.hasOwnProperty(n)&&e.setAttribute(n,this.attributes[n]);for(var a=0;a<this.children.length;a++)e.appendChild(this.children[a].toNode());return e},N=function(t){var e="<"+t;this.classes.length&&(e+=' class="'+c.escape(A(this.classes))+'"');var r="";for(var n in this.style)this.style.hasOwnProperty(n)&&(r+=c.hyphenate(n)+":"+this.style[n]+";");for(var a in r&&(e+=' style="'+c.escape(r)+'"'),this.attributes)this.attributes.hasOwnProperty(a)&&(e+=" "+a+'="'+c.escape(this.attributes[a])+'"');e+=">";for(var i=0;i<this.children.length;i++)e+=this.children[i].toMarkup();return e+="</"+t+">"},q=function(){function t(t,e,r,n){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.width=void 0,this.maxFontSize=void 0,this.style=void 0,B.call(this,t,r,n),this.children=e||[]}var e=t.prototype;return e.setAttribute=function(t,e){this.attributes[t]=e},e.hasClass=function(t){return c.contains(this.classes,t)},e.toNode=function(){return C.call(this,"span")},e.toMarkup=function(){return N.call(this,"span")},t}(),E=function(){function t(t,e,r,n){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,B.call(this,e,n),this.children=r||[],this.setAttribute("href",t)}var e=t.prototype;return e.setAttribute=function(t,e){this.attributes[t]=e},e.hasClass=function(t){return c.contains(this.classes,t)},e.toNode=function(){return C.call(this,"a")},e.toMarkup=function(){return N.call(this,"a")},t}(),O={"\xee":"\u0131\u0302","\xef":"\u0131\u0308","\xed":"\u0131\u0301","\xec":"\u0131\u0300"},I=function(){function t(t,e,r,n,a,i,o,s){this.text=void 0,this.height=void 0,this.depth=void 0,this.italic=void 0,this.skew=void 0,this.width=void 0,this.maxFontSize=void 0,this.classes=void 0,this.style=void 0,this.text=t,this.height=e||0,this.depth=r||0,this.italic=n||0,this.skew=a||0,this.width=i||0,this.classes=o||[],this.style=s||{},this.maxFontSize=0;var h=function(t){for(var e=0;e<k.length;e++)for(var r=k[e],n=0;n<r.blocks.length;n++){var a=r.blocks[n];if(t>=a[0]&&t<=a[1])return r.name}return null}(this.text.charCodeAt(0));h&&this.classes.push(h+"_fallback"),/[\xee\xef\xed\xec]/.test(this.text)&&(this.text=O[this.text])}var e=t.prototype;return e.hasClass=function(t){return c.contains(this.classes,t)},e.toNode=function(){var t=document.createTextNode(this.text),e=null;for(var r in this.italic>0&&((e=document.createElement("span")).style.marginRight=this.italic+"em"),this.classes.length>0&&((e=e||document.createElement("span")).className=A(this.classes)),this.style)this.style.hasOwnProperty(r)&&((e=e||document.createElement("span")).style[r]=this.style[r]);return e?(e.appendChild(t),e):t},e.toMarkup=function(){var t=!1,e="<span";this.classes.length&&(t=!0,e+=' class="',e+=c.escape(A(this.classes)),e+='"');var r="";for(var n in this.italic>0&&(r+="margin-right:"+this.italic+"em;"),this.style)this.style.hasOwnProperty(n)&&(r+=c.hyphenate(n)+":"+this.style[n]+";");r&&(t=!0,e+=' style="'+c.escape(r)+'"');var a=c.escape(this.text);return t?(e+=">",e+=a,e+="</span>"):a},t}(),R=function(){function t(t,e){this.children=void 0,this.attributes=void 0,this.children=t||[],this.attributes=e||{}}var e=t.prototype;return e.toNode=function(){var t=document.createElementNS("http://www.w3.org/2000/svg","svg");for(var e in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,e)&&t.setAttribute(e,this.attributes[e]);for(var r=0;r<this.children.length;r++)t.appendChild(this.children[r].toNode());return t},e.toMarkup=function(){var t="<svg";for(var e in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,e)&&(t+=" "+e+"='"+this.attributes[e]+"'");t+=">";for(var r=0;r<this.children.length;r++)t+=this.children[r].toMarkup();return t+="</svg>"},t}(),L=function(){function t(t,e){this.pathName=void 0,this.alternate=void 0,this.pathName=t,this.alternate=e}var e=t.prototype;return e.toNode=function(){var t=document.createElementNS("http://www.w3.org/2000/svg","path");return this.alternate?t.setAttribute("d",this.alternate):t.setAttribute("d",M.path[this.pathName]),t},e.toMarkup=function(){return this.alternate?"<path d='"+this.alternate+"'/>":"<path d='"+M.path[this.pathName]+"'/>"},t}(),D=function(){function t(t){this.attributes=void 0,this.attributes=t||{}}var e=t.prototype;return e.toNode=function(){var t=document.createElementNS("http://www.w3.org/2000/svg","line");for(var e in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,e)&&t.setAttribute(e,this.attributes[e]);return t},e.toMarkup=function(){var t="<line";for(var e in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,e)&&(t+=" "+e+"='"+this.attributes[e]+"'");return t+="/>"},t}();var H={"AMS-Regular":{65:[0,.68889,0,0,.72222],66:[0,.68889,0,0,.66667],67:[0,.68889,0,0,.72222],68:[0,.68889,0,0,.72222],69:[0,.68889,0,0,.66667],70:[0,.68889,0,0,.61111],71:[0,.68889,0,0,.77778],72:[0,.68889,0,0,.77778],73:[0,.68889,0,0,.38889],74:[.16667,.68889,0,0,.5],75:[0,.68889,0,0,.77778],76:[0,.68889,0,0,.66667],77:[0,.68889,0,0,.94445],78:[0,.68889,0,0,.72222],79:[.16667,.68889,0,0,.77778],80:[0,.68889,0,0,.61111],81:[.16667,.68889,0,0,.77778],82:[0,.68889,0,0,.72222],83:[0,.68889,0,0,.55556],84:[0,.68889,0,0,.66667],85:[0,.68889,0,0,.72222],86:[0,.68889,0,0,.72222],87:[0,.68889,0,0,1],88:[0,.68889,0,0,.72222],89:[0,.68889,0,0,.72222],90:[0,.68889,0,0,.66667],107:[0,.68889,0,0,.55556],165:[0,.675,.025,0,.75],174:[.15559,.69224,0,0,.94666],240:[0,.68889,0,0,.55556],295:[0,.68889,0,0,.54028],710:[0,.825,0,0,2.33334],732:[0,.9,0,0,2.33334],770:[0,.825,0,0,2.33334],771:[0,.9,0,0,2.33334],989:[.08167,.58167,0,0,.77778],1008:[0,.43056,.04028,0,.66667],8245:[0,.54986,0,0,.275],8463:[0,.68889,0,0,.54028],8487:[0,.68889,0,0,.72222],8498:[0,.68889,0,0,.55556],8502:[0,.68889,0,0,.66667],8503:[0,.68889,0,0,.44445],8504:[0,.68889,0,0,.66667],8513:[0,.68889,0,0,.63889],8592:[-.03598,.46402,0,0,.5],8594:[-.03598,.46402,0,0,.5],8602:[-.13313,.36687,0,0,1],8603:[-.13313,.36687,0,0,1],8606:[.01354,.52239,0,0,1],8608:[.01354,.52239,0,0,1],8610:[.01354,.52239,0,0,1.11111],8611:[.01354,.52239,0,0,1.11111],8619:[0,.54986,0,0,1],8620:[0,.54986,0,0,1],8621:[-.13313,.37788,0,0,1.38889],8622:[-.13313,.36687,0,0,1],8624:[0,.69224,0,0,.5],8625:[0,.69224,0,0,.5],8630:[0,.43056,0,0,1],8631:[0,.43056,0,0,1],8634:[.08198,.58198,0,0,.77778],8635:[.08198,.58198,0,0,.77778],8638:[.19444,.69224,0,0,.41667],8639:[.19444,.69224,0,0,.41667],8642:[.19444,.69224,0,0,.41667],8643:[.19444,.69224,0,0,.41667],8644:[.1808,.675,0,0,1],8646:[.1808,.675,0,0,1],8647:[.1808,.675,0,0,1],8648:[.19444,.69224,0,0,.83334],8649:[.1808,.675,0,0,1],8650:[.19444,.69224,0,0,.83334],8651:[.01354,.52239,0,0,1],8652:[.01354,.52239,0,0,1],8653:[-.13313,.36687,0,0,1],8654:[-.13313,.36687,0,0,1],8655:[-.13313,.36687,0,0,1],8666:[.13667,.63667,0,0,1],8667:[.13667,.63667,0,0,1],8669:[-.13313,.37788,0,0,1],8672:[-.064,.437,0,0,1.334],8674:[-.064,.437,0,0,1.334],8705:[0,.825,0,0,.5],8708:[0,.68889,0,0,.55556],8709:[.08167,.58167,0,0,.77778],8717:[0,.43056,0,0,.42917],8722:[-.03598,.46402,0,0,.5],8724:[.08198,.69224,0,0,.77778],8726:[.08167,.58167,0,0,.77778],8733:[0,.69224,0,0,.77778],8736:[0,.69224,0,0,.72222],8737:[0,.69224,0,0,.72222],8738:[.03517,.52239,0,0,.72222],8739:[.08167,.58167,0,0,.22222],8740:[.25142,.74111,0,0,.27778],8741:[.08167,.58167,0,0,.38889],8742:[.25142,.74111,0,0,.5],8756:[0,.69224,0,0,.66667],8757:[0,.69224,0,0,.66667],8764:[-.13313,.36687,0,0,.77778],8765:[-.13313,.37788,0,0,.77778],8769:[-.13313,.36687,0,0,.77778],8770:[-.03625,.46375,0,0,.77778],8774:[.30274,.79383,0,0,.77778],8776:[-.01688,.48312,0,0,.77778],8778:[.08167,.58167,0,0,.77778],8782:[.06062,.54986,0,0,.77778],8783:[.06062,.54986,0,0,.77778],8785:[.08198,.58198,0,0,.77778],8786:[.08198,.58198,0,0,.77778],8787:[.08198,.58198,0,0,.77778],8790:[0,.69224,0,0,.77778],8791:[.22958,.72958,0,0,.77778],8796:[.08198,.91667,0,0,.77778],8806:[.25583,.75583,0,0,.77778],8807:[.25583,.75583,0,0,.77778],8808:[.25142,.75726,0,0,.77778],8809:[.25142,.75726,0,0,.77778],8812:[.25583,.75583,0,0,.5],8814:[.20576,.70576,0,0,.77778],8815:[.20576,.70576,0,0,.77778],8816:[.30274,.79383,0,0,.77778],8817:[.30274,.79383,0,0,.77778],8818:[.22958,.72958,0,0,.77778],8819:[.22958,.72958,0,0,.77778],8822:[.1808,.675,0,0,.77778],8823:[.1808,.675,0,0,.77778],8828:[.13667,.63667,0,0,.77778],8829:[.13667,.63667,0,0,.77778],8830:[.22958,.72958,0,0,.77778],8831:[.22958,.72958,0,0,.77778],8832:[.20576,.70576,0,0,.77778],8833:[.20576,.70576,0,0,.77778],8840:[.30274,.79383,0,0,.77778],8841:[.30274,.79383,0,0,.77778],8842:[.13597,.63597,0,0,.77778],8843:[.13597,.63597,0,0,.77778],8847:[.03517,.54986,0,0,.77778],8848:[.03517,.54986,0,0,.77778],8858:[.08198,.58198,0,0,.77778],8859:[.08198,.58198,0,0,.77778],8861:[.08198,.58198,0,0,.77778],8862:[0,.675,0,0,.77778],8863:[0,.675,0,0,.77778],8864:[0,.675,0,0,.77778],8865:[0,.675,0,0,.77778],8872:[0,.69224,0,0,.61111],8873:[0,.69224,0,0,.72222],8874:[0,.69224,0,0,.88889],8876:[0,.68889,0,0,.61111],8877:[0,.68889,0,0,.61111],8878:[0,.68889,0,0,.72222],8879:[0,.68889,0,0,.72222],8882:[.03517,.54986,0,0,.77778],8883:[.03517,.54986,0,0,.77778],8884:[.13667,.63667,0,0,.77778],8885:[.13667,.63667,0,0,.77778],8888:[0,.54986,0,0,1.11111],8890:[.19444,.43056,0,0,.55556],8891:[.19444,.69224,0,0,.61111],8892:[.19444,.69224,0,0,.61111],8901:[0,.54986,0,0,.27778],8903:[.08167,.58167,0,0,.77778],8905:[.08167,.58167,0,0,.77778],8906:[.08167,.58167,0,0,.77778],8907:[0,.69224,0,0,.77778],8908:[0,.69224,0,0,.77778],8909:[-.03598,.46402,0,0,.77778],8910:[0,.54986,0,0,.76042],8911:[0,.54986,0,0,.76042],8912:[.03517,.54986,0,0,.77778],8913:[.03517,.54986,0,0,.77778],8914:[0,.54986,0,0,.66667],8915:[0,.54986,0,0,.66667],8916:[0,.69224,0,0,.66667],8918:[.0391,.5391,0,0,.77778],8919:[.0391,.5391,0,0,.77778],8920:[.03517,.54986,0,0,1.33334],8921:[.03517,.54986,0,0,1.33334],8922:[.38569,.88569,0,0,.77778],8923:[.38569,.88569,0,0,.77778],8926:[.13667,.63667,0,0,.77778],8927:[.13667,.63667,0,0,.77778],8928:[.30274,.79383,0,0,.77778],8929:[.30274,.79383,0,0,.77778],8934:[.23222,.74111,0,0,.77778],8935:[.23222,.74111,0,0,.77778],8936:[.23222,.74111,0,0,.77778],8937:[.23222,.74111,0,0,.77778],8938:[.20576,.70576,0,0,.77778],8939:[.20576,.70576,0,0,.77778],8940:[.30274,.79383,0,0,.77778],8941:[.30274,.79383,0,0,.77778],8994:[.19444,.69224,0,0,.77778],8995:[.19444,.69224,0,0,.77778],9416:[.15559,.69224,0,0,.90222],9484:[0,.69224,0,0,.5],9488:[0,.69224,0,0,.5],9492:[0,.37788,0,0,.5],9496:[0,.37788,0,0,.5],9585:[.19444,.68889,0,0,.88889],9586:[.19444,.74111,0,0,.88889],9632:[0,.675,0,0,.77778],9633:[0,.675,0,0,.77778],9650:[0,.54986,0,0,.72222],9651:[0,.54986,0,0,.72222],9654:[.03517,.54986,0,0,.77778],9660:[0,.54986,0,0,.72222],9661:[0,.54986,0,0,.72222],9664:[.03517,.54986,0,0,.77778],9674:[.11111,.69224,0,0,.66667],9733:[.19444,.69224,0,0,.94445],10003:[0,.69224,0,0,.83334],10016:[0,.69224,0,0,.83334],10731:[.11111,.69224,0,0,.66667],10846:[.19444,.75583,0,0,.61111],10877:[.13667,.63667,0,0,.77778],10878:[.13667,.63667,0,0,.77778],10885:[.25583,.75583,0,0,.77778],10886:[.25583,.75583,0,0,.77778],10887:[.13597,.63597,0,0,.77778],10888:[.13597,.63597,0,0,.77778],10889:[.26167,.75726,0,0,.77778],10890:[.26167,.75726,0,0,.77778],10891:[.48256,.98256,0,0,.77778],10892:[.48256,.98256,0,0,.77778],10901:[.13667,.63667,0,0,.77778],10902:[.13667,.63667,0,0,.77778],10933:[.25142,.75726,0,0,.77778],10934:[.25142,.75726,0,0,.77778],10935:[.26167,.75726,0,0,.77778],10936:[.26167,.75726,0,0,.77778],10937:[.26167,.75726,0,0,.77778],10938:[.26167,.75726,0,0,.77778],10949:[.25583,.75583,0,0,.77778],10950:[.25583,.75583,0,0,.77778],10955:[.28481,.79383,0,0,.77778],10956:[.28481,.79383,0,0,.77778],57350:[.08167,.58167,0,0,.22222],57351:[.08167,.58167,0,0,.38889],57352:[.08167,.58167,0,0,.77778],57353:[0,.43056,.04028,0,.66667],57356:[.25142,.75726,0,0,.77778],57357:[.25142,.75726,0,0,.77778],57358:[.41951,.91951,0,0,.77778],57359:[.30274,.79383,0,0,.77778],57360:[.30274,.79383,0,0,.77778],57361:[.41951,.91951,0,0,.77778],57366:[.25142,.75726,0,0,.77778],57367:[.25142,.75726,0,0,.77778],57368:[.25142,.75726,0,0,.77778],57369:[.25142,.75726,0,0,.77778],57370:[.13597,.63597,0,0,.77778],57371:[.13597,.63597,0,0,.77778]},"Caligraphic-Regular":{48:[0,.43056,0,0,.5],49:[0,.43056,0,0,.5],50:[0,.43056,0,0,.5],51:[.19444,.43056,0,0,.5],52:[.19444,.43056,0,0,.5],53:[.19444,.43056,0,0,.5],54:[0,.64444,0,0,.5],55:[.19444,.43056,0,0,.5],56:[0,.64444,0,0,.5],57:[.19444,.43056,0,0,.5],65:[0,.68333,0,.19445,.79847],66:[0,.68333,.03041,.13889,.65681],67:[0,.68333,.05834,.13889,.52653],68:[0,.68333,.02778,.08334,.77139],69:[0,.68333,.08944,.11111,.52778],70:[0,.68333,.09931,.11111,.71875],71:[.09722,.68333,.0593,.11111,.59487],72:[0,.68333,.00965,.11111,.84452],73:[0,.68333,.07382,0,.54452],74:[.09722,.68333,.18472,.16667,.67778],75:[0,.68333,.01445,.05556,.76195],76:[0,.68333,0,.13889,.68972],77:[0,.68333,0,.13889,1.2009],78:[0,.68333,.14736,.08334,.82049],79:[0,.68333,.02778,.11111,.79611],80:[0,.68333,.08222,.08334,.69556],81:[.09722,.68333,0,.11111,.81667],82:[0,.68333,0,.08334,.8475],83:[0,.68333,.075,.13889,.60556],84:[0,.68333,.25417,0,.54464],85:[0,.68333,.09931,.08334,.62583],86:[0,.68333,.08222,0,.61278],87:[0,.68333,.08222,.08334,.98778],88:[0,.68333,.14643,.13889,.7133],89:[.09722,.68333,.08222,.08334,.66834],90:[0,.68333,.07944,.13889,.72473]},"Fraktur-Regular":{33:[0,.69141,0,0,.29574],34:[0,.69141,0,0,.21471],38:[0,.69141,0,0,.73786],39:[0,.69141,0,0,.21201],40:[.24982,.74947,0,0,.38865],41:[.24982,.74947,0,0,.38865],42:[0,.62119,0,0,.27764],43:[.08319,.58283,0,0,.75623],44:[0,.10803,0,0,.27764],45:[.08319,.58283,0,0,.75623],46:[0,.10803,0,0,.27764],47:[.24982,.74947,0,0,.50181],48:[0,.47534,0,0,.50181],49:[0,.47534,0,0,.50181],50:[0,.47534,0,0,.50181],51:[.18906,.47534,0,0,.50181],52:[.18906,.47534,0,0,.50181],53:[.18906,.47534,0,0,.50181],54:[0,.69141,0,0,.50181],55:[.18906,.47534,0,0,.50181],56:[0,.69141,0,0,.50181],57:[.18906,.47534,0,0,.50181],58:[0,.47534,0,0,.21606],59:[.12604,.47534,0,0,.21606],61:[-.13099,.36866,0,0,.75623],63:[0,.69141,0,0,.36245],65:[0,.69141,0,0,.7176],66:[0,.69141,0,0,.88397],67:[0,.69141,0,0,.61254],68:[0,.69141,0,0,.83158],69:[0,.69141,0,0,.66278],70:[.12604,.69141,0,0,.61119],71:[0,.69141,0,0,.78539],72:[.06302,.69141,0,0,.7203],73:[0,.69141,0,0,.55448],74:[.12604,.69141,0,0,.55231],75:[0,.69141,0,0,.66845],76:[0,.69141,0,0,.66602],77:[0,.69141,0,0,1.04953],78:[0,.69141,0,0,.83212],79:[0,.69141,0,0,.82699],80:[.18906,.69141,0,0,.82753],81:[.03781,.69141,0,0,.82699],82:[0,.69141,0,0,.82807],83:[0,.69141,0,0,.82861],84:[0,.69141,0,0,.66899],85:[0,.69141,0,0,.64576],86:[0,.69141,0,0,.83131],87:[0,.69141,0,0,1.04602],88:[0,.69141,0,0,.71922],89:[.18906,.69141,0,0,.83293],90:[.12604,.69141,0,0,.60201],91:[.24982,.74947,0,0,.27764],93:[.24982,.74947,0,0,.27764],94:[0,.69141,0,0,.49965],97:[0,.47534,0,0,.50046],98:[0,.69141,0,0,.51315],99:[0,.47534,0,0,.38946],100:[0,.62119,0,0,.49857],101:[0,.47534,0,0,.40053],102:[.18906,.69141,0,0,.32626],103:[.18906,.47534,0,0,.5037],104:[.18906,.69141,0,0,.52126],105:[0,.69141,0,0,.27899],106:[0,.69141,0,0,.28088],107:[0,.69141,0,0,.38946],108:[0,.69141,0,0,.27953],109:[0,.47534,0,0,.76676],110:[0,.47534,0,0,.52666],111:[0,.47534,0,0,.48885],112:[.18906,.52396,0,0,.50046],113:[.18906,.47534,0,0,.48912],114:[0,.47534,0,0,.38919],115:[0,.47534,0,0,.44266],116:[0,.62119,0,0,.33301],117:[0,.47534,0,0,.5172],118:[0,.52396,0,0,.5118],119:[0,.52396,0,0,.77351],120:[.18906,.47534,0,0,.38865],121:[.18906,.47534,0,0,.49884],122:[.18906,.47534,0,0,.39054],8216:[0,.69141,0,0,.21471],8217:[0,.69141,0,0,.21471],58112:[0,.62119,0,0,.49749],58113:[0,.62119,0,0,.4983],58114:[.18906,.69141,0,0,.33328],58115:[.18906,.69141,0,0,.32923],58116:[.18906,.47534,0,0,.50343],58117:[0,.69141,0,0,.33301],58118:[0,.62119,0,0,.33409],58119:[0,.47534,0,0,.50073]},"Main-Bold":{33:[0,.69444,0,0,.35],34:[0,.69444,0,0,.60278],35:[.19444,.69444,0,0,.95833],36:[.05556,.75,0,0,.575],37:[.05556,.75,0,0,.95833],38:[0,.69444,0,0,.89444],39:[0,.69444,0,0,.31944],40:[.25,.75,0,0,.44722],41:[.25,.75,0,0,.44722],42:[0,.75,0,0,.575],43:[.13333,.63333,0,0,.89444],44:[.19444,.15556,0,0,.31944],45:[0,.44444,0,0,.38333],46:[0,.15556,0,0,.31944],47:[.25,.75,0,0,.575],48:[0,.64444,0,0,.575],49:[0,.64444,0,0,.575],50:[0,.64444,0,0,.575],51:[0,.64444,0,0,.575],52:[0,.64444,0,0,.575],53:[0,.64444,0,0,.575],54:[0,.64444,0,0,.575],55:[0,.64444,0,0,.575],56:[0,.64444,0,0,.575],57:[0,.64444,0,0,.575],58:[0,.44444,0,0,.31944],59:[.19444,.44444,0,0,.31944],60:[.08556,.58556,0,0,.89444],61:[-.10889,.39111,0,0,.89444],62:[.08556,.58556,0,0,.89444],63:[0,.69444,0,0,.54305],64:[0,.69444,0,0,.89444],65:[0,.68611,0,0,.86944],66:[0,.68611,0,0,.81805],67:[0,.68611,0,0,.83055],68:[0,.68611,0,0,.88194],69:[0,.68611,0,0,.75555],70:[0,.68611,0,0,.72361],71:[0,.68611,0,0,.90416],72:[0,.68611,0,0,.9],73:[0,.68611,0,0,.43611],74:[0,.68611,0,0,.59444],75:[0,.68611,0,0,.90138],76:[0,.68611,0,0,.69166],77:[0,.68611,0,0,1.09166],78:[0,.68611,0,0,.9],79:[0,.68611,0,0,.86388],80:[0,.68611,0,0,.78611],81:[.19444,.68611,0,0,.86388],82:[0,.68611,0,0,.8625],83:[0,.68611,0,0,.63889],84:[0,.68611,0,0,.8],85:[0,.68611,0,0,.88472],86:[0,.68611,.01597,0,.86944],87:[0,.68611,.01597,0,1.18888],88:[0,.68611,0,0,.86944],89:[0,.68611,.02875,0,.86944],90:[0,.68611,0,0,.70277],91:[.25,.75,0,0,.31944],92:[.25,.75,0,0,.575],93:[.25,.75,0,0,.31944],94:[0,.69444,0,0,.575],95:[.31,.13444,.03194,0,.575],97:[0,.44444,0,0,.55902],98:[0,.69444,0,0,.63889],99:[0,.44444,0,0,.51111],100:[0,.69444,0,0,.63889],101:[0,.44444,0,0,.52708],102:[0,.69444,.10903,0,.35139],103:[.19444,.44444,.01597,0,.575],104:[0,.69444,0,0,.63889],105:[0,.69444,0,0,.31944],106:[.19444,.69444,0,0,.35139],107:[0,.69444,0,0,.60694],108:[0,.69444,0,0,.31944],109:[0,.44444,0,0,.95833],110:[0,.44444,0,0,.63889],111:[0,.44444,0,0,.575],112:[.19444,.44444,0,0,.63889],113:[.19444,.44444,0,0,.60694],114:[0,.44444,0,0,.47361],115:[0,.44444,0,0,.45361],116:[0,.63492,0,0,.44722],117:[0,.44444,0,0,.63889],118:[0,.44444,.01597,0,.60694],119:[0,.44444,.01597,0,.83055],120:[0,.44444,0,0,.60694],121:[.19444,.44444,.01597,0,.60694],122:[0,.44444,0,0,.51111],123:[.25,.75,0,0,.575],124:[.25,.75,0,0,.31944],125:[.25,.75,0,0,.575],126:[.35,.34444,0,0,.575],168:[0,.69444,0,0,.575],172:[0,.44444,0,0,.76666],176:[0,.69444,0,0,.86944],177:[.13333,.63333,0,0,.89444],184:[.17014,0,0,0,.51111],198:[0,.68611,0,0,1.04166],215:[.13333,.63333,0,0,.89444],216:[.04861,.73472,0,0,.89444],223:[0,.69444,0,0,.59722],230:[0,.44444,0,0,.83055],247:[.13333,.63333,0,0,.89444],248:[.09722,.54167,0,0,.575],305:[0,.44444,0,0,.31944],338:[0,.68611,0,0,1.16944],339:[0,.44444,0,0,.89444],567:[.19444,.44444,0,0,.35139],710:[0,.69444,0,0,.575],711:[0,.63194,0,0,.575],713:[0,.59611,0,0,.575],714:[0,.69444,0,0,.575],715:[0,.69444,0,0,.575],728:[0,.69444,0,0,.575],729:[0,.69444,0,0,.31944],730:[0,.69444,0,0,.86944],732:[0,.69444,0,0,.575],733:[0,.69444,0,0,.575],915:[0,.68611,0,0,.69166],916:[0,.68611,0,0,.95833],920:[0,.68611,0,0,.89444],923:[0,.68611,0,0,.80555],926:[0,.68611,0,0,.76666],928:[0,.68611,0,0,.9],931:[0,.68611,0,0,.83055],933:[0,.68611,0,0,.89444],934:[0,.68611,0,0,.83055],936:[0,.68611,0,0,.89444],937:[0,.68611,0,0,.83055],8211:[0,.44444,.03194,0,.575],8212:[0,.44444,.03194,0,1.14999],8216:[0,.69444,0,0,.31944],8217:[0,.69444,0,0,.31944],8220:[0,.69444,0,0,.60278],8221:[0,.69444,0,0,.60278],8224:[.19444,.69444,0,0,.51111],8225:[.19444,.69444,0,0,.51111],8242:[0,.55556,0,0,.34444],8407:[0,.72444,.15486,0,.575],8463:[0,.69444,0,0,.66759],8465:[0,.69444,0,0,.83055],8467:[0,.69444,0,0,.47361],8472:[.19444,.44444,0,0,.74027],8476:[0,.69444,0,0,.83055],8501:[0,.69444,0,0,.70277],8592:[-.10889,.39111,0,0,1.14999],8593:[.19444,.69444,0,0,.575],8594:[-.10889,.39111,0,0,1.14999],8595:[.19444,.69444,0,0,.575],8596:[-.10889,.39111,0,0,1.14999],8597:[.25,.75,0,0,.575],8598:[.19444,.69444,0,0,1.14999],8599:[.19444,.69444,0,0,1.14999],8600:[.19444,.69444,0,0,1.14999],8601:[.19444,.69444,0,0,1.14999],8636:[-.10889,.39111,0,0,1.14999],8637:[-.10889,.39111,0,0,1.14999],8640:[-.10889,.39111,0,0,1.14999],8641:[-.10889,.39111,0,0,1.14999],8656:[-.10889,.39111,0,0,1.14999],8657:[.19444,.69444,0,0,.70277],8658:[-.10889,.39111,0,0,1.14999],8659:[.19444,.69444,0,0,.70277],8660:[-.10889,.39111,0,0,1.14999],8661:[.25,.75,0,0,.70277],8704:[0,.69444,0,0,.63889],8706:[0,.69444,.06389,0,.62847],8707:[0,.69444,0,0,.63889],8709:[.05556,.75,0,0,.575],8711:[0,.68611,0,0,.95833],8712:[.08556,.58556,0,0,.76666],8715:[.08556,.58556,0,0,.76666],8722:[.13333,.63333,0,0,.89444],8723:[.13333,.63333,0,0,.89444],8725:[.25,.75,0,0,.575],8726:[.25,.75,0,0,.575],8727:[-.02778,.47222,0,0,.575],8728:[-.02639,.47361,0,0,.575],8729:[-.02639,.47361,0,0,.575],8730:[.18,.82,0,0,.95833],8733:[0,.44444,0,0,.89444],8734:[0,.44444,0,0,1.14999],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.31944],8741:[.25,.75,0,0,.575],8743:[0,.55556,0,0,.76666],8744:[0,.55556,0,0,.76666],8745:[0,.55556,0,0,.76666],8746:[0,.55556,0,0,.76666],8747:[.19444,.69444,.12778,0,.56875],8764:[-.10889,.39111,0,0,.89444],8768:[.19444,.69444,0,0,.31944],8771:[.00222,.50222,0,0,.89444],8776:[.02444,.52444,0,0,.89444],8781:[.00222,.50222,0,0,.89444],8801:[.00222,.50222,0,0,.89444],8804:[.19667,.69667,0,0,.89444],8805:[.19667,.69667,0,0,.89444],8810:[.08556,.58556,0,0,1.14999],8811:[.08556,.58556,0,0,1.14999],8826:[.08556,.58556,0,0,.89444],8827:[.08556,.58556,0,0,.89444],8834:[.08556,.58556,0,0,.89444],8835:[.08556,.58556,0,0,.89444],8838:[.19667,.69667,0,0,.89444],8839:[.19667,.69667,0,0,.89444],8846:[0,.55556,0,0,.76666],8849:[.19667,.69667,0,0,.89444],8850:[.19667,.69667,0,0,.89444],8851:[0,.55556,0,0,.76666],8852:[0,.55556,0,0,.76666],8853:[.13333,.63333,0,0,.89444],8854:[.13333,.63333,0,0,.89444],8855:[.13333,.63333,0,0,.89444],8856:[.13333,.63333,0,0,.89444],8857:[.13333,.63333,0,0,.89444],8866:[0,.69444,0,0,.70277],8867:[0,.69444,0,0,.70277],8868:[0,.69444,0,0,.89444],8869:[0,.69444,0,0,.89444],8900:[-.02639,.47361,0,0,.575],8901:[-.02639,.47361,0,0,.31944],8902:[-.02778,.47222,0,0,.575],8968:[.25,.75,0,0,.51111],8969:[.25,.75,0,0,.51111],8970:[.25,.75,0,0,.51111],8971:[.25,.75,0,0,.51111],8994:[-.13889,.36111,0,0,1.14999],8995:[-.13889,.36111,0,0,1.14999],9651:[.19444,.69444,0,0,1.02222],9657:[-.02778,.47222,0,0,.575],9661:[.19444,.69444,0,0,1.02222],9667:[-.02778,.47222,0,0,.575],9711:[.19444,.69444,0,0,1.14999],9824:[.12963,.69444,0,0,.89444],9825:[.12963,.69444,0,0,.89444],9826:[.12963,.69444,0,0,.89444],9827:[.12963,.69444,0,0,.89444],9837:[0,.75,0,0,.44722],9838:[.19444,.69444,0,0,.44722],9839:[.19444,.69444,0,0,.44722],10216:[.25,.75,0,0,.44722],10217:[.25,.75,0,0,.44722],10815:[0,.68611,0,0,.9],10927:[.19667,.69667,0,0,.89444],10928:[.19667,.69667,0,0,.89444],57376:[.19444,.69444,0,0,0]},"Main-BoldItalic":{33:[0,.69444,.11417,0,.38611],34:[0,.69444,.07939,0,.62055],35:[.19444,.69444,.06833,0,.94444],37:[.05556,.75,.12861,0,.94444],38:[0,.69444,.08528,0,.88555],39:[0,.69444,.12945,0,.35555],40:[.25,.75,.15806,0,.47333],41:[.25,.75,.03306,0,.47333],42:[0,.75,.14333,0,.59111],43:[.10333,.60333,.03306,0,.88555],44:[.19444,.14722,0,0,.35555],45:[0,.44444,.02611,0,.41444],46:[0,.14722,0,0,.35555],47:[.25,.75,.15806,0,.59111],48:[0,.64444,.13167,0,.59111],49:[0,.64444,.13167,0,.59111],50:[0,.64444,.13167,0,.59111],51:[0,.64444,.13167,0,.59111],52:[.19444,.64444,.13167,0,.59111],53:[0,.64444,.13167,0,.59111],54:[0,.64444,.13167,0,.59111],55:[.19444,.64444,.13167,0,.59111],56:[0,.64444,.13167,0,.59111],57:[0,.64444,.13167,0,.59111],58:[0,.44444,.06695,0,.35555],59:[.19444,.44444,.06695,0,.35555],61:[-.10889,.39111,.06833,0,.88555],63:[0,.69444,.11472,0,.59111],64:[0,.69444,.09208,0,.88555],65:[0,.68611,0,0,.86555],66:[0,.68611,.0992,0,.81666],67:[0,.68611,.14208,0,.82666],68:[0,.68611,.09062,0,.87555],69:[0,.68611,.11431,0,.75666],70:[0,.68611,.12903,0,.72722],71:[0,.68611,.07347,0,.89527],72:[0,.68611,.17208,0,.8961],73:[0,.68611,.15681,0,.47166],74:[0,.68611,.145,0,.61055],75:[0,.68611,.14208,0,.89499],76:[0,.68611,0,0,.69777],77:[0,.68611,.17208,0,1.07277],78:[0,.68611,.17208,0,.8961],79:[0,.68611,.09062,0,.85499],80:[0,.68611,.0992,0,.78721],81:[.19444,.68611,.09062,0,.85499],82:[0,.68611,.02559,0,.85944],83:[0,.68611,.11264,0,.64999],84:[0,.68611,.12903,0,.7961],85:[0,.68611,.17208,0,.88083],86:[0,.68611,.18625,0,.86555],87:[0,.68611,.18625,0,1.15999],88:[0,.68611,.15681,0,.86555],89:[0,.68611,.19803,0,.86555],90:[0,.68611,.14208,0,.70888],91:[.25,.75,.1875,0,.35611],93:[.25,.75,.09972,0,.35611],94:[0,.69444,.06709,0,.59111],95:[.31,.13444,.09811,0,.59111],97:[0,.44444,.09426,0,.59111],98:[0,.69444,.07861,0,.53222],99:[0,.44444,.05222,0,.53222],100:[0,.69444,.10861,0,.59111],101:[0,.44444,.085,0,.53222],102:[.19444,.69444,.21778,0,.4],103:[.19444,.44444,.105,0,.53222],104:[0,.69444,.09426,0,.59111],105:[0,.69326,.11387,0,.35555],106:[.19444,.69326,.1672,0,.35555],107:[0,.69444,.11111,0,.53222],108:[0,.69444,.10861,0,.29666],109:[0,.44444,.09426,0,.94444],110:[0,.44444,.09426,0,.64999],111:[0,.44444,.07861,0,.59111],112:[.19444,.44444,.07861,0,.59111],113:[.19444,.44444,.105,0,.53222],114:[0,.44444,.11111,0,.50167],115:[0,.44444,.08167,0,.48694],116:[0,.63492,.09639,0,.385],117:[0,.44444,.09426,0,.62055],118:[0,.44444,.11111,0,.53222],119:[0,.44444,.11111,0,.76777],120:[0,.44444,.12583,0,.56055],121:[.19444,.44444,.105,0,.56166],122:[0,.44444,.13889,0,.49055],126:[.35,.34444,.11472,0,.59111],163:[0,.69444,0,0,.86853],168:[0,.69444,.11473,0,.59111],176:[0,.69444,0,0,.94888],184:[.17014,0,0,0,.53222],198:[0,.68611,.11431,0,1.02277],216:[.04861,.73472,.09062,0,.88555],223:[.19444,.69444,.09736,0,.665],230:[0,.44444,.085,0,.82666],248:[.09722,.54167,.09458,0,.59111],305:[0,.44444,.09426,0,.35555],338:[0,.68611,.11431,0,1.14054],339:[0,.44444,.085,0,.82666],567:[.19444,.44444,.04611,0,.385],710:[0,.69444,.06709,0,.59111],711:[0,.63194,.08271,0,.59111],713:[0,.59444,.10444,0,.59111],714:[0,.69444,.08528,0,.59111],715:[0,.69444,0,0,.59111],728:[0,.69444,.10333,0,.59111],729:[0,.69444,.12945,0,.35555],730:[0,.69444,0,0,.94888],732:[0,.69444,.11472,0,.59111],733:[0,.69444,.11472,0,.59111],915:[0,.68611,.12903,0,.69777],916:[0,.68611,0,0,.94444],920:[0,.68611,.09062,0,.88555],923:[0,.68611,0,0,.80666],926:[0,.68611,.15092,0,.76777],928:[0,.68611,.17208,0,.8961],931:[0,.68611,.11431,0,.82666],933:[0,.68611,.10778,0,.88555],934:[0,.68611,.05632,0,.82666],936:[0,.68611,.10778,0,.88555],937:[0,.68611,.0992,0,.82666],8211:[0,.44444,.09811,0,.59111],8212:[0,.44444,.09811,0,1.18221],8216:[0,.69444,.12945,0,.35555],8217:[0,.69444,.12945,0,.35555],8220:[0,.69444,.16772,0,.62055],8221:[0,.69444,.07939,0,.62055]},"Main-Italic":{33:[0,.69444,.12417,0,.30667],34:[0,.69444,.06961,0,.51444],35:[.19444,.69444,.06616,0,.81777],37:[.05556,.75,.13639,0,.81777],38:[0,.69444,.09694,0,.76666],39:[0,.69444,.12417,0,.30667],40:[.25,.75,.16194,0,.40889],41:[.25,.75,.03694,0,.40889],42:[0,.75,.14917,0,.51111],43:[.05667,.56167,.03694,0,.76666],44:[.19444,.10556,0,0,.30667],45:[0,.43056,.02826,0,.35778],46:[0,.10556,0,0,.30667],47:[.25,.75,.16194,0,.51111],48:[0,.64444,.13556,0,.51111],49:[0,.64444,.13556,0,.51111],50:[0,.64444,.13556,0,.51111],51:[0,.64444,.13556,0,.51111],52:[.19444,.64444,.13556,0,.51111],53:[0,.64444,.13556,0,.51111],54:[0,.64444,.13556,0,.51111],55:[.19444,.64444,.13556,0,.51111],56:[0,.64444,.13556,0,.51111],57:[0,.64444,.13556,0,.51111],58:[0,.43056,.0582,0,.30667],59:[.19444,.43056,.0582,0,.30667],61:[-.13313,.36687,.06616,0,.76666],63:[0,.69444,.1225,0,.51111],64:[0,.69444,.09597,0,.76666],65:[0,.68333,0,0,.74333],66:[0,.68333,.10257,0,.70389],67:[0,.68333,.14528,0,.71555],68:[0,.68333,.09403,0,.755],69:[0,.68333,.12028,0,.67833],70:[0,.68333,.13305,0,.65277],71:[0,.68333,.08722,0,.77361],72:[0,.68333,.16389,0,.74333],73:[0,.68333,.15806,0,.38555],74:[0,.68333,.14028,0,.525],75:[0,.68333,.14528,0,.76888],76:[0,.68333,0,0,.62722],77:[0,.68333,.16389,0,.89666],78:[0,.68333,.16389,0,.74333],79:[0,.68333,.09403,0,.76666],80:[0,.68333,.10257,0,.67833],81:[.19444,.68333,.09403,0,.76666],82:[0,.68333,.03868,0,.72944],83:[0,.68333,.11972,0,.56222],84:[0,.68333,.13305,0,.71555],85:[0,.68333,.16389,0,.74333],86:[0,.68333,.18361,0,.74333],87:[0,.68333,.18361,0,.99888],88:[0,.68333,.15806,0,.74333],89:[0,.68333,.19383,0,.74333],90:[0,.68333,.14528,0,.61333],91:[.25,.75,.1875,0,.30667],93:[.25,.75,.10528,0,.30667],94:[0,.69444,.06646,0,.51111],95:[.31,.12056,.09208,0,.51111],97:[0,.43056,.07671,0,.51111],98:[0,.69444,.06312,0,.46],99:[0,.43056,.05653,0,.46],100:[0,.69444,.10333,0,.51111],101:[0,.43056,.07514,0,.46],102:[.19444,.69444,.21194,0,.30667],103:[.19444,.43056,.08847,0,.46],104:[0,.69444,.07671,0,.51111],105:[0,.65536,.1019,0,.30667],106:[.19444,.65536,.14467,0,.30667],107:[0,.69444,.10764,0,.46],108:[0,.69444,.10333,0,.25555],109:[0,.43056,.07671,0,.81777],110:[0,.43056,.07671,0,.56222],111:[0,.43056,.06312,0,.51111],112:[.19444,.43056,.06312,0,.51111],113:[.19444,.43056,.08847,0,.46],114:[0,.43056,.10764,0,.42166],115:[0,.43056,.08208,0,.40889],116:[0,.61508,.09486,0,.33222],117:[0,.43056,.07671,0,.53666],118:[0,.43056,.10764,0,.46],119:[0,.43056,.10764,0,.66444],120:[0,.43056,.12042,0,.46389],121:[.19444,.43056,.08847,0,.48555],122:[0,.43056,.12292,0,.40889],126:[.35,.31786,.11585,0,.51111],163:[0,.69444,0,0,.76909],168:[0,.66786,.10474,0,.51111],176:[0,.69444,0,0,.83129],184:[.17014,0,0,0,.46],198:[0,.68333,.12028,0,.88277],216:[.04861,.73194,.09403,0,.76666],223:[.19444,.69444,.10514,0,.53666],230:[0,.43056,.07514,0,.71555],248:[.09722,.52778,.09194,0,.51111],305:[0,.43056,0,.02778,.32246],338:[0,.68333,.12028,0,.98499],339:[0,.43056,.07514,0,.71555],567:[.19444,.43056,0,.08334,.38403],710:[0,.69444,.06646,0,.51111],711:[0,.62847,.08295,0,.51111],713:[0,.56167,.10333,0,.51111],714:[0,.69444,.09694,0,.51111],715:[0,.69444,0,0,.51111],728:[0,.69444,.10806,0,.51111],729:[0,.66786,.11752,0,.30667],730:[0,.69444,0,0,.83129],732:[0,.66786,.11585,0,.51111],733:[0,.69444,.1225,0,.51111],915:[0,.68333,.13305,0,.62722],916:[0,.68333,0,0,.81777],920:[0,.68333,.09403,0,.76666],923:[0,.68333,0,0,.69222],926:[0,.68333,.15294,0,.66444],928:[0,.68333,.16389,0,.74333],931:[0,.68333,.12028,0,.71555],933:[0,.68333,.11111,0,.76666],934:[0,.68333,.05986,0,.71555],936:[0,.68333,.11111,0,.76666],937:[0,.68333,.10257,0,.71555],8211:[0,.43056,.09208,0,.51111],8212:[0,.43056,.09208,0,1.02222],8216:[0,.69444,.12417,0,.30667],8217:[0,.69444,.12417,0,.30667],8220:[0,.69444,.1685,0,.51444],8221:[0,.69444,.06961,0,.51444],8463:[0,.68889,0,0,.54028]},"Main-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.27778],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.77778],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.19444,.10556,0,0,.27778],45:[0,.43056,0,0,.33333],46:[0,.10556,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.64444,0,0,.5],49:[0,.64444,0,0,.5],50:[0,.64444,0,0,.5],51:[0,.64444,0,0,.5],52:[0,.64444,0,0,.5],53:[0,.64444,0,0,.5],54:[0,.64444,0,0,.5],55:[0,.64444,0,0,.5],56:[0,.64444,0,0,.5],57:[0,.64444,0,0,.5],58:[0,.43056,0,0,.27778],59:[.19444,.43056,0,0,.27778],60:[.0391,.5391,0,0,.77778],61:[-.13313,.36687,0,0,.77778],62:[.0391,.5391,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.77778],65:[0,.68333,0,0,.75],66:[0,.68333,0,0,.70834],67:[0,.68333,0,0,.72222],68:[0,.68333,0,0,.76389],69:[0,.68333,0,0,.68056],70:[0,.68333,0,0,.65278],71:[0,.68333,0,0,.78472],72:[0,.68333,0,0,.75],73:[0,.68333,0,0,.36111],74:[0,.68333,0,0,.51389],75:[0,.68333,0,0,.77778],76:[0,.68333,0,0,.625],77:[0,.68333,0,0,.91667],78:[0,.68333,0,0,.75],79:[0,.68333,0,0,.77778],80:[0,.68333,0,0,.68056],81:[.19444,.68333,0,0,.77778],82:[0,.68333,0,0,.73611],83:[0,.68333,0,0,.55556],84:[0,.68333,0,0,.72222],85:[0,.68333,0,0,.75],86:[0,.68333,.01389,0,.75],87:[0,.68333,.01389,0,1.02778],88:[0,.68333,0,0,.75],89:[0,.68333,.025,0,.75],90:[0,.68333,0,0,.61111],91:[.25,.75,0,0,.27778],92:[.25,.75,0,0,.5],93:[.25,.75,0,0,.27778],94:[0,.69444,0,0,.5],95:[.31,.12056,.02778,0,.5],97:[0,.43056,0,0,.5],98:[0,.69444,0,0,.55556],99:[0,.43056,0,0,.44445],100:[0,.69444,0,0,.55556],101:[0,.43056,0,0,.44445],102:[0,.69444,.07778,0,.30556],103:[.19444,.43056,.01389,0,.5],104:[0,.69444,0,0,.55556],105:[0,.66786,0,0,.27778],106:[.19444,.66786,0,0,.30556],107:[0,.69444,0,0,.52778],108:[0,.69444,0,0,.27778],109:[0,.43056,0,0,.83334],110:[0,.43056,0,0,.55556],111:[0,.43056,0,0,.5],112:[.19444,.43056,0,0,.55556],113:[.19444,.43056,0,0,.52778],114:[0,.43056,0,0,.39167],115:[0,.43056,0,0,.39445],116:[0,.61508,0,0,.38889],117:[0,.43056,0,0,.55556],118:[0,.43056,.01389,0,.52778],119:[0,.43056,.01389,0,.72222],120:[0,.43056,0,0,.52778],121:[.19444,.43056,.01389,0,.52778],122:[0,.43056,0,0,.44445],123:[.25,.75,0,0,.5],124:[.25,.75,0,0,.27778],125:[.25,.75,0,0,.5],126:[.35,.31786,0,0,.5],160:[0,0,0,0,.25],167:[.19444,.69444,0,0,.44445],168:[0,.66786,0,0,.5],172:[0,.43056,0,0,.66667],176:[0,.69444,0,0,.75],177:[.08333,.58333,0,0,.77778],182:[.19444,.69444,0,0,.61111],184:[.17014,0,0,0,.44445],198:[0,.68333,0,0,.90278],215:[.08333,.58333,0,0,.77778],216:[.04861,.73194,0,0,.77778],223:[0,.69444,0,0,.5],230:[0,.43056,0,0,.72222],247:[.08333,.58333,0,0,.77778],248:[.09722,.52778,0,0,.5],305:[0,.43056,0,0,.27778],338:[0,.68333,0,0,1.01389],339:[0,.43056,0,0,.77778],567:[.19444,.43056,0,0,.30556],710:[0,.69444,0,0,.5],711:[0,.62847,0,0,.5],713:[0,.56778,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.66786,0,0,.27778],730:[0,.69444,0,0,.75],732:[0,.66786,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.68333,0,0,.625],916:[0,.68333,0,0,.83334],920:[0,.68333,0,0,.77778],923:[0,.68333,0,0,.69445],926:[0,.68333,0,0,.66667],928:[0,.68333,0,0,.75],931:[0,.68333,0,0,.72222],933:[0,.68333,0,0,.77778],934:[0,.68333,0,0,.72222],936:[0,.68333,0,0,.77778],937:[0,.68333,0,0,.72222],8211:[0,.43056,.02778,0,.5],8212:[0,.43056,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5],8224:[.19444,.69444,0,0,.44445],8225:[.19444,.69444,0,0,.44445],8230:[0,.12,0,0,1.172],8242:[0,.55556,0,0,.275],8407:[0,.71444,.15382,0,.5],8463:[0,.68889,0,0,.54028],8465:[0,.69444,0,0,.72222],8467:[0,.69444,0,.11111,.41667],8472:[.19444,.43056,0,.11111,.63646],8476:[0,.69444,0,0,.72222],8501:[0,.69444,0,0,.61111],8592:[-.13313,.36687,0,0,1],8593:[.19444,.69444,0,0,.5],8594:[-.13313,.36687,0,0,1],8595:[.19444,.69444,0,0,.5],8596:[-.13313,.36687,0,0,1],8597:[.25,.75,0,0,.5],8598:[.19444,.69444,0,0,1],8599:[.19444,.69444,0,0,1],8600:[.19444,.69444,0,0,1],8601:[.19444,.69444,0,0,1],8614:[.011,.511,0,0,1],8617:[.011,.511,0,0,1.126],8618:[.011,.511,0,0,1.126],8636:[-.13313,.36687,0,0,1],8637:[-.13313,.36687,0,0,1],8640:[-.13313,.36687,0,0,1],8641:[-.13313,.36687,0,0,1],8652:[.011,.671,0,0,1],8656:[-.13313,.36687,0,0,1],8657:[.19444,.69444,0,0,.61111],8658:[-.13313,.36687,0,0,1],8659:[.19444,.69444,0,0,.61111],8660:[-.13313,.36687,0,0,1],8661:[.25,.75,0,0,.61111],8704:[0,.69444,0,0,.55556],8706:[0,.69444,.05556,.08334,.5309],8707:[0,.69444,0,0,.55556],8709:[.05556,.75,0,0,.5],8711:[0,.68333,0,0,.83334],8712:[.0391,.5391,0,0,.66667],8715:[.0391,.5391,0,0,.66667],8722:[.08333,.58333,0,0,.77778],8723:[.08333,.58333,0,0,.77778],8725:[.25,.75,0,0,.5],8726:[.25,.75,0,0,.5],8727:[-.03472,.46528,0,0,.5],8728:[-.05555,.44445,0,0,.5],8729:[-.05555,.44445,0,0,.5],8730:[.2,.8,0,0,.83334],8733:[0,.43056,0,0,.77778],8734:[0,.43056,0,0,1],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.27778],8741:[.25,.75,0,0,.5],8743:[0,.55556,0,0,.66667],8744:[0,.55556,0,0,.66667],8745:[0,.55556,0,0,.66667],8746:[0,.55556,0,0,.66667],8747:[.19444,.69444,.11111,0,.41667],8764:[-.13313,.36687,0,0,.77778],8768:[.19444,.69444,0,0,.27778],8771:[-.03625,.46375,0,0,.77778],8773:[-.022,.589,0,0,1],8776:[-.01688,.48312,0,0,.77778],8781:[-.03625,.46375,0,0,.77778],8784:[-.133,.67,0,0,.778],8801:[-.03625,.46375,0,0,.77778],8804:[.13597,.63597,0,0,.77778],8805:[.13597,.63597,0,0,.77778],8810:[.0391,.5391,0,0,1],8811:[.0391,.5391,0,0,1],8826:[.0391,.5391,0,0,.77778],8827:[.0391,.5391,0,0,.77778],8834:[.0391,.5391,0,0,.77778],8835:[.0391,.5391,0,0,.77778],8838:[.13597,.63597,0,0,.77778],8839:[.13597,.63597,0,0,.77778],8846:[0,.55556,0,0,.66667],8849:[.13597,.63597,0,0,.77778],8850:[.13597,.63597,0,0,.77778],8851:[0,.55556,0,0,.66667],8852:[0,.55556,0,0,.66667],8853:[.08333,.58333,0,0,.77778],8854:[.08333,.58333,0,0,.77778],8855:[.08333,.58333,0,0,.77778],8856:[.08333,.58333,0,0,.77778],8857:[.08333,.58333,0,0,.77778],8866:[0,.69444,0,0,.61111],8867:[0,.69444,0,0,.61111],8868:[0,.69444,0,0,.77778],8869:[0,.69444,0,0,.77778],8872:[.249,.75,0,0,.867],8900:[-.05555,.44445,0,0,.5],8901:[-.05555,.44445,0,0,.27778],8902:[-.03472,.46528,0,0,.5],8904:[.005,.505,0,0,.9],8942:[.03,.9,0,0,.278],8943:[-.19,.31,0,0,1.172],8945:[-.1,.82,0,0,1.282],8968:[.25,.75,0,0,.44445],8969:[.25,.75,0,0,.44445],8970:[.25,.75,0,0,.44445],8971:[.25,.75,0,0,.44445],8994:[-.14236,.35764,0,0,1],8995:[-.14236,.35764,0,0,1],9136:[.244,.744,0,0,.412],9137:[.244,.744,0,0,.412],9651:[.19444,.69444,0,0,.88889],9657:[-.03472,.46528,0,0,.5],9661:[.19444,.69444,0,0,.88889],9667:[-.03472,.46528,0,0,.5],9711:[.19444,.69444,0,0,1],9824:[.12963,.69444,0,0,.77778],9825:[.12963,.69444,0,0,.77778],9826:[.12963,.69444,0,0,.77778],9827:[.12963,.69444,0,0,.77778],9837:[0,.75,0,0,.38889],9838:[.19444,.69444,0,0,.38889],9839:[.19444,.69444,0,0,.38889],10216:[.25,.75,0,0,.38889],10217:[.25,.75,0,0,.38889],10222:[.244,.744,0,0,.412],10223:[.244,.744,0,0,.412],10229:[.011,.511,0,0,1.609],10230:[.011,.511,0,0,1.638],10231:[.011,.511,0,0,1.859],10232:[.024,.525,0,0,1.609],10233:[.024,.525,0,0,1.638],10234:[.024,.525,0,0,1.858],10236:[.011,.511,0,0,1.638],10815:[0,.68333,0,0,.75],10927:[.13597,.63597,0,0,.77778],10928:[.13597,.63597,0,0,.77778],57376:[.19444,.69444,0,0,0]},"Math-BoldItalic":{65:[0,.68611,0,0,.86944],66:[0,.68611,.04835,0,.8664],67:[0,.68611,.06979,0,.81694],68:[0,.68611,.03194,0,.93812],69:[0,.68611,.05451,0,.81007],70:[0,.68611,.15972,0,.68889],71:[0,.68611,0,0,.88673],72:[0,.68611,.08229,0,.98229],73:[0,.68611,.07778,0,.51111],74:[0,.68611,.10069,0,.63125],75:[0,.68611,.06979,0,.97118],76:[0,.68611,0,0,.75555],77:[0,.68611,.11424,0,1.14201],78:[0,.68611,.11424,0,.95034],79:[0,.68611,.03194,0,.83666],80:[0,.68611,.15972,0,.72309],81:[.19444,.68611,0,0,.86861],82:[0,.68611,.00421,0,.87235],83:[0,.68611,.05382,0,.69271],84:[0,.68611,.15972,0,.63663],85:[0,.68611,.11424,0,.80027],86:[0,.68611,.25555,0,.67778],87:[0,.68611,.15972,0,1.09305],88:[0,.68611,.07778,0,.94722],89:[0,.68611,.25555,0,.67458],90:[0,.68611,.06979,0,.77257],97:[0,.44444,0,0,.63287],98:[0,.69444,0,0,.52083],99:[0,.44444,0,0,.51342],100:[0,.69444,0,0,.60972],101:[0,.44444,0,0,.55361],102:[.19444,.69444,.11042,0,.56806],103:[.19444,.44444,.03704,0,.5449],104:[0,.69444,0,0,.66759],105:[0,.69326,0,0,.4048],106:[.19444,.69326,.0622,0,.47083],107:[0,.69444,.01852,0,.6037],108:[0,.69444,.0088,0,.34815],109:[0,.44444,0,0,1.0324],110:[0,.44444,0,0,.71296],111:[0,.44444,0,0,.58472],112:[.19444,.44444,0,0,.60092],113:[.19444,.44444,.03704,0,.54213],114:[0,.44444,.03194,0,.5287],115:[0,.44444,0,0,.53125],116:[0,.63492,0,0,.41528],117:[0,.44444,0,0,.68102],118:[0,.44444,.03704,0,.56666],119:[0,.44444,.02778,0,.83148],120:[0,.44444,0,0,.65903],121:[.19444,.44444,.03704,0,.59028],122:[0,.44444,.04213,0,.55509],915:[0,.68611,.15972,0,.65694],916:[0,.68611,0,0,.95833],920:[0,.68611,.03194,0,.86722],923:[0,.68611,0,0,.80555],926:[0,.68611,.07458,0,.84125],928:[0,.68611,.08229,0,.98229],931:[0,.68611,.05451,0,.88507],933:[0,.68611,.15972,0,.67083],934:[0,.68611,0,0,.76666],936:[0,.68611,.11653,0,.71402],937:[0,.68611,.04835,0,.8789],945:[0,.44444,0,0,.76064],946:[.19444,.69444,.03403,0,.65972],947:[.19444,.44444,.06389,0,.59003],948:[0,.69444,.03819,0,.52222],949:[0,.44444,0,0,.52882],950:[.19444,.69444,.06215,0,.50833],951:[.19444,.44444,.03704,0,.6],952:[0,.69444,.03194,0,.5618],953:[0,.44444,0,0,.41204],954:[0,.44444,0,0,.66759],955:[0,.69444,0,0,.67083],956:[.19444,.44444,0,0,.70787],957:[0,.44444,.06898,0,.57685],958:[.19444,.69444,.03021,0,.50833],959:[0,.44444,0,0,.58472],960:[0,.44444,.03704,0,.68241],961:[.19444,.44444,0,0,.6118],962:[.09722,.44444,.07917,0,.42361],963:[0,.44444,.03704,0,.68588],964:[0,.44444,.13472,0,.52083],965:[0,.44444,.03704,0,.63055],966:[.19444,.44444,0,0,.74722],967:[.19444,.44444,0,0,.71805],968:[.19444,.69444,.03704,0,.75833],969:[0,.44444,.03704,0,.71782],977:[0,.69444,0,0,.69155],981:[.19444,.69444,0,0,.7125],982:[0,.44444,.03194,0,.975],1009:[.19444,.44444,0,0,.6118],1013:[0,.44444,0,0,.48333]},"Math-Italic":{65:[0,.68333,0,.13889,.75],66:[0,.68333,.05017,.08334,.75851],67:[0,.68333,.07153,.08334,.71472],68:[0,.68333,.02778,.05556,.82792],69:[0,.68333,.05764,.08334,.7382],70:[0,.68333,.13889,.08334,.64306],71:[0,.68333,0,.08334,.78625],72:[0,.68333,.08125,.05556,.83125],73:[0,.68333,.07847,.11111,.43958],74:[0,.68333,.09618,.16667,.55451],75:[0,.68333,.07153,.05556,.84931],76:[0,.68333,0,.02778,.68056],77:[0,.68333,.10903,.08334,.97014],78:[0,.68333,.10903,.08334,.80347],79:[0,.68333,.02778,.08334,.76278],80:[0,.68333,.13889,.08334,.64201],81:[.19444,.68333,0,.08334,.79056],82:[0,.68333,.00773,.08334,.75929],83:[0,.68333,.05764,.08334,.6132],84:[0,.68333,.13889,.08334,.58438],85:[0,.68333,.10903,.02778,.68278],86:[0,.68333,.22222,0,.58333],87:[0,.68333,.13889,0,.94445],88:[0,.68333,.07847,.08334,.82847],89:[0,.68333,.22222,0,.58056],90:[0,.68333,.07153,.08334,.68264],97:[0,.43056,0,0,.52859],98:[0,.69444,0,0,.42917],99:[0,.43056,0,.05556,.43276],100:[0,.69444,0,.16667,.52049],101:[0,.43056,0,.05556,.46563],102:[.19444,.69444,.10764,.16667,.48959],103:[.19444,.43056,.03588,.02778,.47697],104:[0,.69444,0,0,.57616],105:[0,.65952,0,0,.34451],106:[.19444,.65952,.05724,0,.41181],107:[0,.69444,.03148,0,.5206],108:[0,.69444,.01968,.08334,.29838],109:[0,.43056,0,0,.87801],110:[0,.43056,0,0,.60023],111:[0,.43056,0,.05556,.48472],112:[.19444,.43056,0,.08334,.50313],113:[.19444,.43056,.03588,.08334,.44641],114:[0,.43056,.02778,.05556,.45116],115:[0,.43056,0,.05556,.46875],116:[0,.61508,0,.08334,.36111],117:[0,.43056,0,.02778,.57246],118:[0,.43056,.03588,.02778,.48472],119:[0,.43056,.02691,.08334,.71592],120:[0,.43056,0,.02778,.57153],121:[.19444,.43056,.03588,.05556,.49028],122:[0,.43056,.04398,.05556,.46505],915:[0,.68333,.13889,.08334,.61528],916:[0,.68333,0,.16667,.83334],920:[0,.68333,.02778,.08334,.76278],923:[0,.68333,0,.16667,.69445],926:[0,.68333,.07569,.08334,.74236],928:[0,.68333,.08125,.05556,.83125],931:[0,.68333,.05764,.08334,.77986],933:[0,.68333,.13889,.05556,.58333],934:[0,.68333,0,.08334,.66667],936:[0,.68333,.11,.05556,.61222],937:[0,.68333,.05017,.08334,.7724],945:[0,.43056,.0037,.02778,.6397],946:[.19444,.69444,.05278,.08334,.56563],947:[.19444,.43056,.05556,0,.51773],948:[0,.69444,.03785,.05556,.44444],949:[0,.43056,0,.08334,.46632],950:[.19444,.69444,.07378,.08334,.4375],951:[.19444,.43056,.03588,.05556,.49653],952:[0,.69444,.02778,.08334,.46944],953:[0,.43056,0,.05556,.35394],954:[0,.43056,0,0,.57616],955:[0,.69444,0,0,.58334],956:[.19444,.43056,0,.02778,.60255],957:[0,.43056,.06366,.02778,.49398],958:[.19444,.69444,.04601,.11111,.4375],959:[0,.43056,0,.05556,.48472],960:[0,.43056,.03588,0,.57003],961:[.19444,.43056,0,.08334,.51702],962:[.09722,.43056,.07986,.08334,.36285],963:[0,.43056,.03588,0,.57141],964:[0,.43056,.1132,.02778,.43715],965:[0,.43056,.03588,.02778,.54028],966:[.19444,.43056,0,.08334,.65417],967:[.19444,.43056,0,.05556,.62569],968:[.19444,.69444,.03588,.11111,.65139],969:[0,.43056,.03588,0,.62245],977:[0,.69444,0,.08334,.59144],981:[.19444,.69444,0,.08334,.59583],982:[0,.43056,.02778,0,.82813],1009:[.19444,.43056,0,.08334,.51702],1013:[0,.43056,0,.05556,.4059]},"Math-Regular":{65:[0,.68333,0,.13889,.75],66:[0,.68333,.05017,.08334,.75851],67:[0,.68333,.07153,.08334,.71472],68:[0,.68333,.02778,.05556,.82792],69:[0,.68333,.05764,.08334,.7382],70:[0,.68333,.13889,.08334,.64306],71:[0,.68333,0,.08334,.78625],72:[0,.68333,.08125,.05556,.83125],73:[0,.68333,.07847,.11111,.43958],74:[0,.68333,.09618,.16667,.55451],75:[0,.68333,.07153,.05556,.84931],76:[0,.68333,0,.02778,.68056],77:[0,.68333,.10903,.08334,.97014],78:[0,.68333,.10903,.08334,.80347],79:[0,.68333,.02778,.08334,.76278],80:[0,.68333,.13889,.08334,.64201],81:[.19444,.68333,0,.08334,.79056],82:[0,.68333,.00773,.08334,.75929],83:[0,.68333,.05764,.08334,.6132],84:[0,.68333,.13889,.08334,.58438],85:[0,.68333,.10903,.02778,.68278],86:[0,.68333,.22222,0,.58333],87:[0,.68333,.13889,0,.94445],88:[0,.68333,.07847,.08334,.82847],89:[0,.68333,.22222,0,.58056],90:[0,.68333,.07153,.08334,.68264],97:[0,.43056,0,0,.52859],98:[0,.69444,0,0,.42917],99:[0,.43056,0,.05556,.43276],100:[0,.69444,0,.16667,.52049],101:[0,.43056,0,.05556,.46563],102:[.19444,.69444,.10764,.16667,.48959],103:[.19444,.43056,.03588,.02778,.47697],104:[0,.69444,0,0,.57616],105:[0,.65952,0,0,.34451],106:[.19444,.65952,.05724,0,.41181],107:[0,.69444,.03148,0,.5206],108:[0,.69444,.01968,.08334,.29838],109:[0,.43056,0,0,.87801],110:[0,.43056,0,0,.60023],111:[0,.43056,0,.05556,.48472],112:[.19444,.43056,0,.08334,.50313],113:[.19444,.43056,.03588,.08334,.44641],114:[0,.43056,.02778,.05556,.45116],115:[0,.43056,0,.05556,.46875],116:[0,.61508,0,.08334,.36111],117:[0,.43056,0,.02778,.57246],118:[0,.43056,.03588,.02778,.48472],119:[0,.43056,.02691,.08334,.71592],120:[0,.43056,0,.02778,.57153],121:[.19444,.43056,.03588,.05556,.49028],122:[0,.43056,.04398,.05556,.46505],915:[0,.68333,.13889,.08334,.61528],916:[0,.68333,0,.16667,.83334],920:[0,.68333,.02778,.08334,.76278],923:[0,.68333,0,.16667,.69445],926:[0,.68333,.07569,.08334,.74236],928:[0,.68333,.08125,.05556,.83125],931:[0,.68333,.05764,.08334,.77986],933:[0,.68333,.13889,.05556,.58333],934:[0,.68333,0,.08334,.66667],936:[0,.68333,.11,.05556,.61222],937:[0,.68333,.05017,.08334,.7724],945:[0,.43056,.0037,.02778,.6397],946:[.19444,.69444,.05278,.08334,.56563],947:[.19444,.43056,.05556,0,.51773],948:[0,.69444,.03785,.05556,.44444],949:[0,.43056,0,.08334,.46632],950:[.19444,.69444,.07378,.08334,.4375],951:[.19444,.43056,.03588,.05556,.49653],952:[0,.69444,.02778,.08334,.46944],953:[0,.43056,0,.05556,.35394],954:[0,.43056,0,0,.57616],955:[0,.69444,0,0,.58334],956:[.19444,.43056,0,.02778,.60255],957:[0,.43056,.06366,.02778,.49398],958:[.19444,.69444,.04601,.11111,.4375],959:[0,.43056,0,.05556,.48472],960:[0,.43056,.03588,0,.57003],961:[.19444,.43056,0,.08334,.51702],962:[.09722,.43056,.07986,.08334,.36285],963:[0,.43056,.03588,0,.57141],964:[0,.43056,.1132,.02778,.43715],965:[0,.43056,.03588,.02778,.54028],966:[.19444,.43056,0,.08334,.65417],967:[.19444,.43056,0,.05556,.62569],968:[.19444,.69444,.03588,.11111,.65139],969:[0,.43056,.03588,0,.62245],977:[0,.69444,0,.08334,.59144],981:[.19444,.69444,0,.08334,.59583],982:[0,.43056,.02778,0,.82813],1009:[.19444,.43056,0,.08334,.51702],1013:[0,.43056,0,.05556,.4059]},"SansSerif-Bold":{33:[0,.69444,0,0,.36667],34:[0,.69444,0,0,.55834],35:[.19444,.69444,0,0,.91667],36:[.05556,.75,0,0,.55],37:[.05556,.75,0,0,1.02912],38:[0,.69444,0,0,.83056],39:[0,.69444,0,0,.30556],40:[.25,.75,0,0,.42778],41:[.25,.75,0,0,.42778],42:[0,.75,0,0,.55],43:[.11667,.61667,0,0,.85556],44:[.10556,.13056,0,0,.30556],45:[0,.45833,0,0,.36667],46:[0,.13056,0,0,.30556],47:[.25,.75,0,0,.55],48:[0,.69444,0,0,.55],49:[0,.69444,0,0,.55],50:[0,.69444,0,0,.55],51:[0,.69444,0,0,.55],52:[0,.69444,0,0,.55],53:[0,.69444,0,0,.55],54:[0,.69444,0,0,.55],55:[0,.69444,0,0,.55],56:[0,.69444,0,0,.55],57:[0,.69444,0,0,.55],58:[0,.45833,0,0,.30556],59:[.10556,.45833,0,0,.30556],61:[-.09375,.40625,0,0,.85556],63:[0,.69444,0,0,.51945],64:[0,.69444,0,0,.73334],65:[0,.69444,0,0,.73334],66:[0,.69444,0,0,.73334],67:[0,.69444,0,0,.70278],68:[0,.69444,0,0,.79445],69:[0,.69444,0,0,.64167],70:[0,.69444,0,0,.61111],71:[0,.69444,0,0,.73334],72:[0,.69444,0,0,.79445],73:[0,.69444,0,0,.33056],74:[0,.69444,0,0,.51945],75:[0,.69444,0,0,.76389],76:[0,.69444,0,0,.58056],77:[0,.69444,0,0,.97778],78:[0,.69444,0,0,.79445],79:[0,.69444,0,0,.79445],80:[0,.69444,0,0,.70278],81:[.10556,.69444,0,0,.79445],82:[0,.69444,0,0,.70278],83:[0,.69444,0,0,.61111],84:[0,.69444,0,0,.73334],85:[0,.69444,0,0,.76389],86:[0,.69444,.01528,0,.73334],87:[0,.69444,.01528,0,1.03889],88:[0,.69444,0,0,.73334],89:[0,.69444,.0275,0,.73334],90:[0,.69444,0,0,.67223],91:[.25,.75,0,0,.34306],93:[.25,.75,0,0,.34306],94:[0,.69444,0,0,.55],95:[.35,.10833,.03056,0,.55],97:[0,.45833,0,0,.525],98:[0,.69444,0,0,.56111],99:[0,.45833,0,0,.48889],100:[0,.69444,0,0,.56111],101:[0,.45833,0,0,.51111],102:[0,.69444,.07639,0,.33611],103:[.19444,.45833,.01528,0,.55],104:[0,.69444,0,0,.56111],105:[0,.69444,0,0,.25556],106:[.19444,.69444,0,0,.28611],107:[0,.69444,0,0,.53056],108:[0,.69444,0,0,.25556],109:[0,.45833,0,0,.86667],110:[0,.45833,0,0,.56111],111:[0,.45833,0,0,.55],112:[.19444,.45833,0,0,.56111],113:[.19444,.45833,0,0,.56111],114:[0,.45833,.01528,0,.37222],115:[0,.45833,0,0,.42167],116:[0,.58929,0,0,.40417],117:[0,.45833,0,0,.56111],118:[0,.45833,.01528,0,.5],119:[0,.45833,.01528,0,.74445],120:[0,.45833,0,0,.5],121:[.19444,.45833,.01528,0,.5],122:[0,.45833,0,0,.47639],126:[.35,.34444,0,0,.55],168:[0,.69444,0,0,.55],176:[0,.69444,0,0,.73334],180:[0,.69444,0,0,.55],184:[.17014,0,0,0,.48889],305:[0,.45833,0,0,.25556],567:[.19444,.45833,0,0,.28611],710:[0,.69444,0,0,.55],711:[0,.63542,0,0,.55],713:[0,.63778,0,0,.55],728:[0,.69444,0,0,.55],729:[0,.69444,0,0,.30556],730:[0,.69444,0,0,.73334],732:[0,.69444,0,0,.55],733:[0,.69444,0,0,.55],915:[0,.69444,0,0,.58056],916:[0,.69444,0,0,.91667],920:[0,.69444,0,0,.85556],923:[0,.69444,0,0,.67223],926:[0,.69444,0,0,.73334],928:[0,.69444,0,0,.79445],931:[0,.69444,0,0,.79445],933:[0,.69444,0,0,.85556],934:[0,.69444,0,0,.79445],936:[0,.69444,0,0,.85556],937:[0,.69444,0,0,.79445],8211:[0,.45833,.03056,0,.55],8212:[0,.45833,.03056,0,1.10001],8216:[0,.69444,0,0,.30556],8217:[0,.69444,0,0,.30556],8220:[0,.69444,0,0,.55834],8221:[0,.69444,0,0,.55834]},"SansSerif-Italic":{33:[0,.69444,.05733,0,.31945],34:[0,.69444,.00316,0,.5],35:[.19444,.69444,.05087,0,.83334],36:[.05556,.75,.11156,0,.5],37:[.05556,.75,.03126,0,.83334],38:[0,.69444,.03058,0,.75834],39:[0,.69444,.07816,0,.27778],40:[.25,.75,.13164,0,.38889],41:[.25,.75,.02536,0,.38889],42:[0,.75,.11775,0,.5],43:[.08333,.58333,.02536,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,.01946,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,.13164,0,.5],48:[0,.65556,.11156,0,.5],49:[0,.65556,.11156,0,.5],50:[0,.65556,.11156,0,.5],51:[0,.65556,.11156,0,.5],52:[0,.65556,.11156,0,.5],53:[0,.65556,.11156,0,.5],54:[0,.65556,.11156,0,.5],55:[0,.65556,.11156,0,.5],56:[0,.65556,.11156,0,.5],57:[0,.65556,.11156,0,.5],58:[0,.44444,.02502,0,.27778],59:[.125,.44444,.02502,0,.27778],61:[-.13,.37,.05087,0,.77778],63:[0,.69444,.11809,0,.47222],64:[0,.69444,.07555,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,.08293,0,.66667],67:[0,.69444,.11983,0,.63889],68:[0,.69444,.07555,0,.72223],69:[0,.69444,.11983,0,.59722],70:[0,.69444,.13372,0,.56945],71:[0,.69444,.11983,0,.66667],72:[0,.69444,.08094,0,.70834],73:[0,.69444,.13372,0,.27778],74:[0,.69444,.08094,0,.47222],75:[0,.69444,.11983,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,.08094,0,.875],78:[0,.69444,.08094,0,.70834],79:[0,.69444,.07555,0,.73611],80:[0,.69444,.08293,0,.63889],81:[.125,.69444,.07555,0,.73611],82:[0,.69444,.08293,0,.64584],83:[0,.69444,.09205,0,.55556],84:[0,.69444,.13372,0,.68056],85:[0,.69444,.08094,0,.6875],86:[0,.69444,.1615,0,.66667],87:[0,.69444,.1615,0,.94445],88:[0,.69444,.13372,0,.66667],89:[0,.69444,.17261,0,.66667],90:[0,.69444,.11983,0,.61111],91:[.25,.75,.15942,0,.28889],93:[.25,.75,.08719,0,.28889],94:[0,.69444,.0799,0,.5],95:[.35,.09444,.08616,0,.5],97:[0,.44444,.00981,0,.48056],98:[0,.69444,.03057,0,.51667],99:[0,.44444,.08336,0,.44445],100:[0,.69444,.09483,0,.51667],101:[0,.44444,.06778,0,.44445],102:[0,.69444,.21705,0,.30556],103:[.19444,.44444,.10836,0,.5],104:[0,.69444,.01778,0,.51667],105:[0,.67937,.09718,0,.23889],106:[.19444,.67937,.09162,0,.26667],107:[0,.69444,.08336,0,.48889],108:[0,.69444,.09483,0,.23889],109:[0,.44444,.01778,0,.79445],110:[0,.44444,.01778,0,.51667],111:[0,.44444,.06613,0,.5],112:[.19444,.44444,.0389,0,.51667],113:[.19444,.44444,.04169,0,.51667],114:[0,.44444,.10836,0,.34167],115:[0,.44444,.0778,0,.38333],116:[0,.57143,.07225,0,.36111],117:[0,.44444,.04169,0,.51667],118:[0,.44444,.10836,0,.46111],119:[0,.44444,.10836,0,.68334],120:[0,.44444,.09169,0,.46111],121:[.19444,.44444,.10836,0,.46111],122:[0,.44444,.08752,0,.43472],126:[.35,.32659,.08826,0,.5],168:[0,.67937,.06385,0,.5],176:[0,.69444,0,0,.73752],184:[.17014,0,0,0,.44445],305:[0,.44444,.04169,0,.23889],567:[.19444,.44444,.04169,0,.26667],710:[0,.69444,.0799,0,.5],711:[0,.63194,.08432,0,.5],713:[0,.60889,.08776,0,.5],714:[0,.69444,.09205,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,.09483,0,.5],729:[0,.67937,.07774,0,.27778],730:[0,.69444,0,0,.73752],732:[0,.67659,.08826,0,.5],733:[0,.69444,.09205,0,.5],915:[0,.69444,.13372,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,.07555,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,.12816,0,.66667],928:[0,.69444,.08094,0,.70834],931:[0,.69444,.11983,0,.72222],933:[0,.69444,.09031,0,.77778],934:[0,.69444,.04603,0,.72222],936:[0,.69444,.09031,0,.77778],937:[0,.69444,.08293,0,.72222],8211:[0,.44444,.08616,0,.5],8212:[0,.44444,.08616,0,1],8216:[0,.69444,.07816,0,.27778],8217:[0,.69444,.07816,0,.27778],8220:[0,.69444,.14205,0,.5],8221:[0,.69444,.00316,0,.5]},"SansSerif-Regular":{33:[0,.69444,0,0,.31945],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.75834],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,0,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.65556,0,0,.5],49:[0,.65556,0,0,.5],50:[0,.65556,0,0,.5],51:[0,.65556,0,0,.5],52:[0,.65556,0,0,.5],53:[0,.65556,0,0,.5],54:[0,.65556,0,0,.5],55:[0,.65556,0,0,.5],56:[0,.65556,0,0,.5],57:[0,.65556,0,0,.5],58:[0,.44444,0,0,.27778],59:[.125,.44444,0,0,.27778],61:[-.13,.37,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,0,0,.66667],67:[0,.69444,0,0,.63889],68:[0,.69444,0,0,.72223],69:[0,.69444,0,0,.59722],70:[0,.69444,0,0,.56945],71:[0,.69444,0,0,.66667],72:[0,.69444,0,0,.70834],73:[0,.69444,0,0,.27778],74:[0,.69444,0,0,.47222],75:[0,.69444,0,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,0,0,.875],78:[0,.69444,0,0,.70834],79:[0,.69444,0,0,.73611],80:[0,.69444,0,0,.63889],81:[.125,.69444,0,0,.73611],82:[0,.69444,0,0,.64584],83:[0,.69444,0,0,.55556],84:[0,.69444,0,0,.68056],85:[0,.69444,0,0,.6875],86:[0,.69444,.01389,0,.66667],87:[0,.69444,.01389,0,.94445],88:[0,.69444,0,0,.66667],89:[0,.69444,.025,0,.66667],90:[0,.69444,0,0,.61111],91:[.25,.75,0,0,.28889],93:[.25,.75,0,0,.28889],94:[0,.69444,0,0,.5],95:[.35,.09444,.02778,0,.5],97:[0,.44444,0,0,.48056],98:[0,.69444,0,0,.51667],99:[0,.44444,0,0,.44445],100:[0,.69444,0,0,.51667],101:[0,.44444,0,0,.44445],102:[0,.69444,.06944,0,.30556],103:[.19444,.44444,.01389,0,.5],104:[0,.69444,0,0,.51667],105:[0,.67937,0,0,.23889],106:[.19444,.67937,0,0,.26667],107:[0,.69444,0,0,.48889],108:[0,.69444,0,0,.23889],109:[0,.44444,0,0,.79445],110:[0,.44444,0,0,.51667],111:[0,.44444,0,0,.5],112:[.19444,.44444,0,0,.51667],113:[.19444,.44444,0,0,.51667],114:[0,.44444,.01389,0,.34167],115:[0,.44444,0,0,.38333],116:[0,.57143,0,0,.36111],117:[0,.44444,0,0,.51667],118:[0,.44444,.01389,0,.46111],119:[0,.44444,.01389,0,.68334],120:[0,.44444,0,0,.46111],121:[.19444,.44444,.01389,0,.46111],122:[0,.44444,0,0,.43472],126:[.35,.32659,0,0,.5],168:[0,.67937,0,0,.5],176:[0,.69444,0,0,.66667],184:[.17014,0,0,0,.44445],305:[0,.44444,0,0,.23889],567:[.19444,.44444,0,0,.26667],710:[0,.69444,0,0,.5],711:[0,.63194,0,0,.5],713:[0,.60889,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.67937,0,0,.27778],730:[0,.69444,0,0,.66667],732:[0,.67659,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.69444,0,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,0,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,0,0,.66667],928:[0,.69444,0,0,.70834],931:[0,.69444,0,0,.72222],933:[0,.69444,0,0,.77778],934:[0,.69444,0,0,.72222],936:[0,.69444,0,0,.77778],937:[0,.69444,0,0,.72222],8211:[0,.44444,.02778,0,.5],8212:[0,.44444,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5]},"Script-Regular":{65:[0,.7,.22925,0,.80253],66:[0,.7,.04087,0,.90757],67:[0,.7,.1689,0,.66619],68:[0,.7,.09371,0,.77443],69:[0,.7,.18583,0,.56162],70:[0,.7,.13634,0,.89544],71:[0,.7,.17322,0,.60961],72:[0,.7,.29694,0,.96919],73:[0,.7,.19189,0,.80907],74:[.27778,.7,.19189,0,1.05159],75:[0,.7,.31259,0,.91364],76:[0,.7,.19189,0,.87373],77:[0,.7,.15981,0,1.08031],78:[0,.7,.3525,0,.9015],79:[0,.7,.08078,0,.73787],80:[0,.7,.08078,0,1.01262],81:[0,.7,.03305,0,.88282],82:[0,.7,.06259,0,.85],83:[0,.7,.19189,0,.86767],84:[0,.7,.29087,0,.74697],85:[0,.7,.25815,0,.79996],86:[0,.7,.27523,0,.62204],87:[0,.7,.27523,0,.80532],88:[0,.7,.26006,0,.94445],89:[0,.7,.2939,0,.70961],90:[0,.7,.24037,0,.8212]},"Size1-Regular":{40:[.35001,.85,0,0,.45834],41:[.35001,.85,0,0,.45834],47:[.35001,.85,0,0,.57778],91:[.35001,.85,0,0,.41667],92:[.35001,.85,0,0,.57778],93:[.35001,.85,0,0,.41667],123:[.35001,.85,0,0,.58334],125:[.35001,.85,0,0,.58334],710:[0,.72222,0,0,.55556],732:[0,.72222,0,0,.55556],770:[0,.72222,0,0,.55556],771:[0,.72222,0,0,.55556],8214:[-99e-5,.601,0,0,.77778],8593:[1e-5,.6,0,0,.66667],8595:[1e-5,.6,0,0,.66667],8657:[1e-5,.6,0,0,.77778],8659:[1e-5,.6,0,0,.77778],8719:[.25001,.75,0,0,.94445],8720:[.25001,.75,0,0,.94445],8721:[.25001,.75,0,0,1.05556],8730:[.35001,.85,0,0,1],8739:[-.00599,.606,0,0,.33333],8741:[-.00599,.606,0,0,.55556],8747:[.30612,.805,.19445,0,.47222],8748:[.306,.805,.19445,0,.47222],8749:[.306,.805,.19445,0,.47222],8750:[.30612,.805,.19445,0,.47222],8896:[.25001,.75,0,0,.83334],8897:[.25001,.75,0,0,.83334],8898:[.25001,.75,0,0,.83334],8899:[.25001,.75,0,0,.83334],8968:[.35001,.85,0,0,.47222],8969:[.35001,.85,0,0,.47222],8970:[.35001,.85,0,0,.47222],8971:[.35001,.85,0,0,.47222],9168:[-99e-5,.601,0,0,.66667],10216:[.35001,.85,0,0,.47222],10217:[.35001,.85,0,0,.47222],10752:[.25001,.75,0,0,1.11111],10753:[.25001,.75,0,0,1.11111],10754:[.25001,.75,0,0,1.11111],10756:[.25001,.75,0,0,.83334],10758:[.25001,.75,0,0,.83334]},"Size2-Regular":{40:[.65002,1.15,0,0,.59722],41:[.65002,1.15,0,0,.59722],47:[.65002,1.15,0,0,.81111],91:[.65002,1.15,0,0,.47222],92:[.65002,1.15,0,0,.81111],93:[.65002,1.15,0,0,.47222],123:[.65002,1.15,0,0,.66667],125:[.65002,1.15,0,0,.66667],710:[0,.75,0,0,1],732:[0,.75,0,0,1],770:[0,.75,0,0,1],771:[0,.75,0,0,1],8719:[.55001,1.05,0,0,1.27778],8720:[.55001,1.05,0,0,1.27778],8721:[.55001,1.05,0,0,1.44445],8730:[.65002,1.15,0,0,1],8747:[.86225,1.36,.44445,0,.55556],8748:[.862,1.36,.44445,0,.55556],8749:[.862,1.36,.44445,0,.55556],8750:[.86225,1.36,.44445,0,.55556],8896:[.55001,1.05,0,0,1.11111],8897:[.55001,1.05,0,0,1.11111],8898:[.55001,1.05,0,0,1.11111],8899:[.55001,1.05,0,0,1.11111],8968:[.65002,1.15,0,0,.52778],8969:[.65002,1.15,0,0,.52778],8970:[.65002,1.15,0,0,.52778],8971:[.65002,1.15,0,0,.52778],10216:[.65002,1.15,0,0,.61111],10217:[.65002,1.15,0,0,.61111],10752:[.55001,1.05,0,0,1.51112],10753:[.55001,1.05,0,0,1.51112],10754:[.55001,1.05,0,0,1.51112],10756:[.55001,1.05,0,0,1.11111],10758:[.55001,1.05,0,0,1.11111]},"Size3-Regular":{40:[.95003,1.45,0,0,.73611],41:[.95003,1.45,0,0,.73611],47:[.95003,1.45,0,0,1.04445],91:[.95003,1.45,0,0,.52778],92:[.95003,1.45,0,0,1.04445],93:[.95003,1.45,0,0,.52778],123:[.95003,1.45,0,0,.75],125:[.95003,1.45,0,0,.75],710:[0,.75,0,0,1.44445],732:[0,.75,0,0,1.44445],770:[0,.75,0,0,1.44445],771:[0,.75,0,0,1.44445],8730:[.95003,1.45,0,0,1],8968:[.95003,1.45,0,0,.58334],8969:[.95003,1.45,0,0,.58334],8970:[.95003,1.45,0,0,.58334],8971:[.95003,1.45,0,0,.58334],10216:[.95003,1.45,0,0,.75],10217:[.95003,1.45,0,0,.75]},"Size4-Regular":{40:[1.25003,1.75,0,0,.79167],41:[1.25003,1.75,0,0,.79167],47:[1.25003,1.75,0,0,1.27778],91:[1.25003,1.75,0,0,.58334],92:[1.25003,1.75,0,0,1.27778],93:[1.25003,1.75,0,0,.58334],123:[1.25003,1.75,0,0,.80556],125:[1.25003,1.75,0,0,.80556],710:[0,.825,0,0,1.8889],732:[0,.825,0,0,1.8889],770:[0,.825,0,0,1.8889],771:[0,.825,0,0,1.8889],8730:[1.25003,1.75,0,0,1],8968:[1.25003,1.75,0,0,.63889],8969:[1.25003,1.75,0,0,.63889],8970:[1.25003,1.75,0,0,.63889],8971:[1.25003,1.75,0,0,.63889],9115:[.64502,1.155,0,0,.875],9116:[1e-5,.6,0,0,.875],9117:[.64502,1.155,0,0,.875],9118:[.64502,1.155,0,0,.875],9119:[1e-5,.6,0,0,.875],9120:[.64502,1.155,0,0,.875],9121:[.64502,1.155,0,0,.66667],9122:[-99e-5,.601,0,0,.66667],9123:[.64502,1.155,0,0,.66667],9124:[.64502,1.155,0,0,.66667],9125:[-99e-5,.601,0,0,.66667],9126:[.64502,1.155,0,0,.66667],9127:[1e-5,.9,0,0,.88889],9128:[.65002,1.15,0,0,.88889],9129:[.90001,0,0,0,.88889],9130:[0,.3,0,0,.88889],9131:[1e-5,.9,0,0,.88889],9132:[.65002,1.15,0,0,.88889],9133:[.90001,0,0,0,.88889],9143:[.88502,.915,0,0,1.05556],10216:[1.25003,1.75,0,0,.80556],10217:[1.25003,1.75,0,0,.80556],57344:[-.00499,.605,0,0,1.05556],57345:[-.00499,.605,0,0,1.05556],57680:[0,.12,0,0,.45],57681:[0,.12,0,0,.45],57682:[0,.12,0,0,.45],57683:[0,.12,0,0,.45]},"Typewriter-Regular":{32:[0,0,0,0,.525],33:[0,.61111,0,0,.525],34:[0,.61111,0,0,.525],35:[0,.61111,0,0,.525],36:[.08333,.69444,0,0,.525],37:[.08333,.69444,0,0,.525],38:[0,.61111,0,0,.525],39:[0,.61111,0,0,.525],40:[.08333,.69444,0,0,.525],41:[.08333,.69444,0,0,.525],42:[0,.52083,0,0,.525],43:[-.08056,.53055,0,0,.525],44:[.13889,.125,0,0,.525],45:[-.08056,.53055,0,0,.525],46:[0,.125,0,0,.525],47:[.08333,.69444,0,0,.525],48:[0,.61111,0,0,.525],49:[0,.61111,0,0,.525],50:[0,.61111,0,0,.525],51:[0,.61111,0,0,.525],52:[0,.61111,0,0,.525],53:[0,.61111,0,0,.525],54:[0,.61111,0,0,.525],55:[0,.61111,0,0,.525],56:[0,.61111,0,0,.525],57:[0,.61111,0,0,.525],58:[0,.43056,0,0,.525],59:[.13889,.43056,0,0,.525],60:[-.05556,.55556,0,0,.525],61:[-.19549,.41562,0,0,.525],62:[-.05556,.55556,0,0,.525],63:[0,.61111,0,0,.525],64:[0,.61111,0,0,.525],65:[0,.61111,0,0,.525],66:[0,.61111,0,0,.525],67:[0,.61111,0,0,.525],68:[0,.61111,0,0,.525],69:[0,.61111,0,0,.525],70:[0,.61111,0,0,.525],71:[0,.61111,0,0,.525],72:[0,.61111,0,0,.525],73:[0,.61111,0,0,.525],74:[0,.61111,0,0,.525],75:[0,.61111,0,0,.525],76:[0,.61111,0,0,.525],77:[0,.61111,0,0,.525],78:[0,.61111,0,0,.525],79:[0,.61111,0,0,.525],80:[0,.61111,0,0,.525],81:[.13889,.61111,0,0,.525],82:[0,.61111,0,0,.525],83:[0,.61111,0,0,.525],84:[0,.61111,0,0,.525],85:[0,.61111,0,0,.525],86:[0,.61111,0,0,.525],87:[0,.61111,0,0,.525],88:[0,.61111,0,0,.525],89:[0,.61111,0,0,.525],90:[0,.61111,0,0,.525],91:[.08333,.69444,0,0,.525],92:[.08333,.69444,0,0,.525],93:[.08333,.69444,0,0,.525],94:[0,.61111,0,0,.525],95:[.09514,0,0,0,.525],96:[0,.61111,0,0,.525],97:[0,.43056,0,0,.525],98:[0,.61111,0,0,.525],99:[0,.43056,0,0,.525],100:[0,.61111,0,0,.525],101:[0,.43056,0,0,.525],102:[0,.61111,0,0,.525],103:[.22222,.43056,0,0,.525],104:[0,.61111,0,0,.525],105:[0,.61111,0,0,.525],106:[.22222,.61111,0,0,.525],107:[0,.61111,0,0,.525],108:[0,.61111,0,0,.525],109:[0,.43056,0,0,.525],110:[0,.43056,0,0,.525],111:[0,.43056,0,0,.525],112:[.22222,.43056,0,0,.525],113:[.22222,.43056,0,0,.525],114:[0,.43056,0,0,.525],115:[0,.43056,0,0,.525],116:[0,.55358,0,0,.525],117:[0,.43056,0,0,.525],118:[0,.43056,0,0,.525],119:[0,.43056,0,0,.525],120:[0,.43056,0,0,.525],121:[.22222,.43056,0,0,.525],122:[0,.43056,0,0,.525],123:[.08333,.69444,0,0,.525],124:[.08333,.69444,0,0,.525],125:[.08333,.69444,0,0,.525],126:[0,.61111,0,0,.525],127:[0,.61111,0,0,.525],160:[0,0,0,0,.525],176:[0,.61111,0,0,.525],184:[.19445,0,0,0,.525],305:[0,.43056,0,0,.525],567:[.22222,.43056,0,0,.525],711:[0,.56597,0,0,.525],713:[0,.56555,0,0,.525],714:[0,.61111,0,0,.525],715:[0,.61111,0,0,.525],728:[0,.61111,0,0,.525],730:[0,.61111,0,0,.525],770:[0,.61111,0,0,.525],771:[0,.61111,0,0,.525],776:[0,.61111,0,0,.525],915:[0,.61111,0,0,.525],916:[0,.61111,0,0,.525],920:[0,.61111,0,0,.525],923:[0,.61111,0,0,.525],926:[0,.61111,0,0,.525],928:[0,.61111,0,0,.525],931:[0,.61111,0,0,.525],933:[0,.61111,0,0,.525],934:[0,.61111,0,0,.525],936:[0,.61111,0,0,.525],937:[0,.61111,0,0,.525],8216:[0,.61111,0,0,.525],8217:[0,.61111,0,0,.525],8242:[0,.61111,0,0,.525],9251:[.11111,.21944,0,0,.525]}},P={slant:[.25,.25,.25],space:[0,0,0],stretch:[0,0,0],shrink:[0,0,0],xHeight:[.431,.431,.431],quad:[1,1.171,1.472],extraSpace:[0,0,0],num1:[.677,.732,.925],num2:[.394,.384,.387],num3:[.444,.471,.504],denom1:[.686,.752,1.025],denom2:[.345,.344,.532],sup1:[.413,.503,.504],sup2:[.363,.431,.404],sup3:[.289,.286,.294],sub1:[.15,.143,.2],sub2:[.247,.286,.4],supDrop:[.386,.353,.494],subDrop:[.05,.071,.1],delim1:[2.39,1.7,1.98],delim2:[1.01,1.157,1.42],axisHeight:[.25,.25,.25],defaultRuleThickness:[.04,.049,.049],bigOpSpacing1:[.111,.111,.111],bigOpSpacing2:[.166,.166,.166],bigOpSpacing3:[.2,.2,.2],bigOpSpacing4:[.6,.611,.611],bigOpSpacing5:[.1,.143,.143],sqrtRuleThickness:[.04,.04,.04],ptPerEm:[10,10,10],doubleRuleSep:[.2,.2,.2]},F={"\xc5":"A","\xc7":"C","\xd0":"D","\xde":"o","\xe5":"a","\xe7":"c","\xf0":"d","\xfe":"o","\u0410":"A","\u0411":"B","\u0412":"B","\u0413":"F","\u0414":"A","\u0415":"E","\u0416":"K","\u0417":"3","\u0418":"N","\u0419":"N","\u041a":"K","\u041b":"N","\u041c":"M","\u041d":"H","\u041e":"O","\u041f":"N","\u0420":"P","\u0421":"C","\u0422":"T","\u0423":"y","\u0424":"O","\u0425":"X","\u0426":"U","\u0427":"h","\u0428":"W","\u0429":"W","\u042a":"B","\u042b":"X","\u042c":"B","\u042d":"3","\u042e":"X","\u042f":"R","\u0430":"a","\u0431":"b","\u0432":"a","\u0433":"r","\u0434":"y","\u0435":"e","\u0436":"m","\u0437":"e","\u0438":"n","\u0439":"n","\u043a":"n","\u043b":"n","\u043c":"m","\u043d":"n","\u043e":"o","\u043f":"n","\u0440":"p","\u0441":"c","\u0442":"o","\u0443":"y","\u0444":"b","\u0445":"x","\u0446":"n","\u0447":"n","\u0448":"w","\u0449":"w","\u044a":"a","\u044b":"m","\u044c":"a","\u044d":"e","\u044e":"m","\u044f":"r"};function V(t,e,r){if(!H[e])throw new Error("Font metrics not found for font: "+e+".");var n=t.charCodeAt(0);t[0]in F&&(n=F[t[0]].charCodeAt(0));var a=H[e][n];if(a||"text"!==r||z(n)&&(a=H[e][77]),a)return{depth:a[0],height:a[1],italic:a[2],skew:a[3],width:a[4]}}var U={};var G={bin:1,close:1,inner:1,open:1,punct:1,rel:1},X={"accent-token":1,mathord:1,"op-token":1,spacing:1,textord:1},Y={math:{},text:{}},_=Y;function W(t,e,r,n,a,i){Y[t][a]={font:e,group:r,replace:n},i&&n&&(Y[t][n]=Y[t][a])}var j="main",$="ams",Z="bin",K="mathord",J="op-token",Q="rel",tt="spacing";W("math",j,Q,"\u2261","\\equiv",!0),W("math",j,Q,"\u227a","\\prec",!0),W("math",j,Q,"\u227b","\\succ",!0),W("math",j,Q,"\u223c","\\sim",!0),W("math",j,Q,"\u22a5","\\perp"),W("math",j,Q,"\u2aaf","\\preceq",!0),W("math",j,Q,"\u2ab0","\\succeq",!0),W("math",j,Q,"\u2243","\\simeq",!0),W("math",j,Q,"\u2223","\\mid",!0),W("math",j,Q,"\u226a","\\ll",!0),W("math",j,Q,"\u226b","\\gg",!0),W("math",j,Q,"\u224d","\\asymp",!0),W("math",j,Q,"\u2225","\\parallel"),W("math",j,Q,"\u22c8","\\bowtie",!0),W("math",j,Q,"\u2323","\\smile",!0),W("math",j,Q,"\u2291","\\sqsubseteq",!0),W("math",j,Q,"\u2292","\\sqsupseteq",!0),W("math",j,Q,"\u2250","\\doteq",!0),W("math",j,Q,"\u2322","\\frown",!0),W("math",j,Q,"\u220b","\\ni",!0),W("math",j,Q,"\u221d","\\propto",!0),W("math",j,Q,"\u22a2","\\vdash",!0),W("math",j,Q,"\u22a3","\\dashv",!0),W("math",j,Q,"\u220b","\\owns"),W("math",j,"punct",".","\\ldotp"),W("math",j,"punct","\u22c5","\\cdotp"),W("math",j,"textord","#","\\#"),W("text",j,"textord","#","\\#"),W("math",j,"textord","&","\\&"),W("text",j,"textord","&","\\&"),W("math",j,"textord","\u2135","\\aleph",!0),W("math",j,"textord","\u2200","\\forall",!0),W("math",j,"textord","\u210f","\\hbar",!0),W("math",j,"textord","\u2203","\\exists",!0),W("math",j,"textord","\u2207","\\nabla",!0),W("math",j,"textord","\u266d","\\flat",!0),W("math",j,"textord","\u2113","\\ell",!0),W("math",j,"textord","\u266e","\\natural",!0),W("math",j,"textord","\u2663","\\clubsuit",!0),W("math",j,"textord","\u2118","\\wp",!0),W("math",j,"textord","\u266f","\\sharp",!0),W("math",j,"textord","\u2662","\\diamondsuit",!0),W("math",j,"textord","\u211c","\\Re",!0),W("math",j,"textord","\u2661","\\heartsuit",!0),W("math",j,"textord","\u2111","\\Im",!0),W("math",j,"textord","\u2660","\\spadesuit",!0),W("text",j,"textord","\xa7","\\S",!0),W("text",j,"textord","\xb6","\\P",!0),W("math",j,"textord","\u2020","\\dag"),W("text",j,"textord","\u2020","\\dag"),W("text",j,"textord","\u2020","\\textdagger"),W("math",j,"textord","\u2021","\\ddag"),W("text",j,"textord","\u2021","\\ddag"),W("text",j,"textord","\u2021","\\textdaggerdbl"),W("math",j,"close","\u23b1","\\rmoustache",!0),W("math",j,"open","\u23b0","\\lmoustache",!0),W("math",j,"close","\u27ef","\\rgroup",!0),W("math",j,"open","\u27ee","\\lgroup",!0),W("math",j,Z,"\u2213","\\mp",!0),W("math",j,Z,"\u2296","\\ominus",!0),W("math",j,Z,"\u228e","\\uplus",!0),W("math",j,Z,"\u2293","\\sqcap",!0),W("math",j,Z,"\u2217","\\ast"),W("math",j,Z,"\u2294","\\sqcup",!0),W("math",j,Z,"\u25ef","\\bigcirc"),W("math",j,Z,"\u2219","\\bullet"),W("math",j,Z,"\u2021","\\ddagger"),W("math",j,Z,"\u2240","\\wr",!0),W("math",j,Z,"\u2a3f","\\amalg"),W("math",j,Z,"&","\\And"),W("math",j,Q,"\u27f5","\\longleftarrow",!0),W("math",j,Q,"\u21d0","\\Leftarrow",!0),W("math",j,Q,"\u27f8","\\Longleftarrow",!0),W("math",j,Q,"\u27f6","\\longrightarrow",!0),W("math",j,Q,"\u21d2","\\Rightarrow",!0),W("math",j,Q,"\u27f9","\\Longrightarrow",!0),W("math",j,Q,"\u2194","\\leftrightarrow",!0),W("math",j,Q,"\u27f7","\\longleftrightarrow",!0),W("math",j,Q,"\u21d4","\\Leftrightarrow",!0),W("math",j,Q,"\u27fa","\\Longleftrightarrow",!0),W("math",j,Q,"\u21a6","\\mapsto",!0),W("math",j,Q,"\u27fc","\\longmapsto",!0),W("math",j,Q,"\u2197","\\nearrow",!0),W("math",j,Q,"\u21a9","\\hookleftarrow",!0),W("math",j,Q,"\u21aa","\\hookrightarrow",!0),W("math",j,Q,"\u2198","\\searrow",!0),W("math",j,Q,"\u21bc","\\leftharpoonup",!0),W("math",j,Q,"\u21c0","\\rightharpoonup",!0),W("math",j,Q,"\u2199","\\swarrow",!0),W("math",j,Q,"\u21bd","\\leftharpoondown",!0),W("math",j,Q,"\u21c1","\\rightharpoondown",!0),W("math",j,Q,"\u2196","\\nwarrow",!0),W("math",j,Q,"\u21cc","\\rightleftharpoons",!0),W("math",$,Q,"\u226e","\\nless",!0),W("math",$,Q,"\ue010","\\nleqslant"),W("math",$,Q,"\ue011","\\nleqq"),W("math",$,Q,"\u2a87","\\lneq",!0),W("math",$,Q,"\u2268","\\lneqq",!0),W("math",$,Q,"\ue00c","\\lvertneqq"),W("math",$,Q,"\u22e6","\\lnsim",!0),W("math",$,Q,"\u2a89","\\lnapprox",!0),W("math",$,Q,"\u2280","\\nprec",!0),W("math",$,Q,"\u22e0","\\npreceq",!0),W("math",$,Q,"\u22e8","\\precnsim",!0),W("math",$,Q,"\u2ab9","\\precnapprox",!0),W("math",$,Q,"\u2241","\\nsim",!0),W("math",$,Q,"\ue006","\\nshortmid"),W("math",$,Q,"\u2224","\\nmid",!0),W("math",$,Q,"\u22ac","\\nvdash",!0),W("math",$,Q,"\u22ad","\\nvDash",!0),W("math",$,Q,"\u22ea","\\ntriangleleft"),W("math",$,Q,"\u22ec","\\ntrianglelefteq",!0),W("math",$,Q,"\u228a","\\subsetneq",!0),W("math",$,Q,"\ue01a","\\varsubsetneq"),W("math",$,Q,"\u2acb","\\subsetneqq",!0),W("math",$,Q,"\ue017","\\varsubsetneqq"),W("math",$,Q,"\u226f","\\ngtr",!0),W("math",$,Q,"\ue00f","\\ngeqslant"),W("math",$,Q,"\ue00e","\\ngeqq"),W("math",$,Q,"\u2a88","\\gneq",!0),W("math",$,Q,"\u2269","\\gneqq",!0),W("math",$,Q,"\ue00d","\\gvertneqq"),W("math",$,Q,"\u22e7","\\gnsim",!0),W("math",$,Q,"\u2a8a","\\gnapprox",!0),W("math",$,Q,"\u2281","\\nsucc",!0),W("math",$,Q,"\u22e1","\\nsucceq",!0),W("math",$,Q,"\u22e9","\\succnsim",!0),W("math",$,Q,"\u2aba","\\succnapprox",!0),W("math",$,Q,"\u2246","\\ncong",!0),W("math",$,Q,"\ue007","\\nshortparallel"),W("math",$,Q,"\u2226","\\nparallel",!0),W("math",$,Q,"\u22af","\\nVDash",!0),W("math",$,Q,"\u22eb","\\ntriangleright"),W("math",$,Q,"\u22ed","\\ntrianglerighteq",!0),W("math",$,Q,"\ue018","\\nsupseteqq"),W("math",$,Q,"\u228b","\\supsetneq",!0),W("math",$,Q,"\ue01b","\\varsupsetneq"),W("math",$,Q,"\u2acc","\\supsetneqq",!0),W("math",$,Q,"\ue019","\\varsupsetneqq"),W("math",$,Q,"\u22ae","\\nVdash",!0),W("math",$,Q,"\u2ab5","\\precneqq",!0),W("math",$,Q,"\u2ab6","\\succneqq",!0),W("math",$,Q,"\ue016","\\nsubseteqq"),W("math",$,Z,"\u22b4","\\unlhd"),W("math",$,Z,"\u22b5","\\unrhd"),W("math",$,Q,"\u219a","\\nleftarrow",!0),W("math",$,Q,"\u219b","\\nrightarrow",!0),W("math",$,Q,"\u21cd","\\nLeftarrow",!0),W("math",$,Q,"\u21cf","\\nRightarrow",!0),W("math",$,Q,"\u21ae","\\nleftrightarrow",!0),W("math",$,Q,"\u21ce","\\nLeftrightarrow",!0),W("math",$,Q,"\u25b3","\\vartriangle"),W("math",$,"textord","\u210f","\\hslash"),W("math",$,"textord","\u25bd","\\triangledown"),W("math",$,"textord","\u25ca","\\lozenge"),W("math",$,"textord","\u24c8","\\circledS"),W("math",$,"textord","\xae","\\circledR"),W("text",$,"textord","\xae","\\circledR"),W("math",$,"textord","\u2221","\\measuredangle",!0),W("math",$,"textord","\u2204","\\nexists"),W("math",$,"textord","\u2127","\\mho"),W("math",$,"textord","\u2132","\\Finv",!0),W("math",$,"textord","\u2141","\\Game",!0),W("math",$,"textord","k","\\Bbbk"),W("math",$,"textord","\u2035","\\backprime"),W("math",$,"textord","\u25b2","\\blacktriangle"),W("math",$,"textord","\u25bc","\\blacktriangledown"),W("math",$,"textord","\u25a0","\\blacksquare"),W("math",$,"textord","\u29eb","\\blacklozenge"),W("math",$,"textord","\u2605","\\bigstar"),W("math",$,"textord","\u2222","\\sphericalangle",!0),W("math",$,"textord","\u2201","\\complement",!0),W("math",$,"textord","\xf0","\\eth",!0),W("math",$,"textord","\u2571","\\diagup"),W("math",$,"textord","\u2572","\\diagdown"),W("math",$,"textord","\u25a1","\\square"),W("math",$,"textord","\u25a1","\\Box"),W("math",$,"textord","\u25ca","\\Diamond"),W("math",$,"textord","\xa5","\\yen",!0),W("text",$,"textord","\xa5","\\yen",!0),W("math",$,"textord","\u2713","\\checkmark",!0),W("text",$,"textord","\u2713","\\checkmark"),W("math",$,"textord","\u2136","\\beth",!0),W("math",$,"textord","\u2138","\\daleth",!0),W("math",$,"textord","\u2137","\\gimel",!0),W("math",$,"textord","\u03dd","\\digamma"),W("math",$,"textord","\u03f0","\\varkappa"),W("math",$,"open","\u250c","\\ulcorner",!0),W("math",$,"close","\u2510","\\urcorner",!0),W("math",$,"open","\u2514","\\llcorner",!0),W("math",$,"close","\u2518","\\lrcorner",!0),W("math",$,Q,"\u2266","\\leqq",!0),W("math",$,Q,"\u2a7d","\\leqslant",!0),W("math",$,Q,"\u2a95","\\eqslantless",!0),W("math",$,Q,"\u2272","\\lesssim",!0),W("math",$,Q,"\u2a85","\\lessapprox",!0),W("math",$,Q,"\u224a","\\approxeq",!0),W("math",$,Z,"\u22d6","\\lessdot"),W("math",$,Q,"\u22d8","\\lll",!0),W("math",$,Q,"\u2276","\\lessgtr",!0),W("math",$,Q,"\u22da","\\lesseqgtr",!0),W("math",$,Q,"\u2a8b","\\lesseqqgtr",!0),W("math",$,Q,"\u2251","\\doteqdot"),W("math",$,Q,"\u2253","\\risingdotseq",!0),W("math",$,Q,"\u2252","\\fallingdotseq",!0),W("math",$,Q,"\u223d","\\backsim",!0),W("math",$,Q,"\u22cd","\\backsimeq",!0),W("math",$,Q,"\u2ac5","\\subseteqq",!0),W("math",$,Q,"\u22d0","\\Subset",!0),W("math",$,Q,"\u228f","\\sqsubset",!0),W("math",$,Q,"\u227c","\\preccurlyeq",!0),W("math",$,Q,"\u22de","\\curlyeqprec",!0),W("math",$,Q,"\u227e","\\precsim",!0),W("math",$,Q,"\u2ab7","\\precapprox",!0),W("math",$,Q,"\u22b2","\\vartriangleleft"),W("math",$,Q,"\u22b4","\\trianglelefteq"),W("math",$,Q,"\u22a8","\\vDash",!0),W("math",$,Q,"\u22aa","\\Vvdash",!0),W("math",$,Q,"\u2323","\\smallsmile"),W("math",$,Q,"\u2322","\\smallfrown"),W("math",$,Q,"\u224f","\\bumpeq",!0),W("math",$,Q,"\u224e","\\Bumpeq",!0),W("math",$,Q,"\u2267","\\geqq",!0),W("math",$,Q,"\u2a7e","\\geqslant",!0),W("math",$,Q,"\u2a96","\\eqslantgtr",!0),W("math",$,Q,"\u2273","\\gtrsim",!0),W("math",$,Q,"\u2a86","\\gtrapprox",!0),W("math",$,Z,"\u22d7","\\gtrdot"),W("math",$,Q,"\u22d9","\\ggg",!0),W("math",$,Q,"\u2277","\\gtrless",!0),W("math",$,Q,"\u22db","\\gtreqless",!0),W("math",$,Q,"\u2a8c","\\gtreqqless",!0),W("math",$,Q,"\u2256","\\eqcirc",!0),W("math",$,Q,"\u2257","\\circeq",!0),W("math",$,Q,"\u225c","\\triangleq",!0),W("math",$,Q,"\u223c","\\thicksim"),W("math",$,Q,"\u2248","\\thickapprox"),W("math",$,Q,"\u2ac6","\\supseteqq",!0),W("math",$,Q,"\u22d1","\\Supset",!0),W("math",$,Q,"\u2290","\\sqsupset",!0),W("math",$,Q,"\u227d","\\succcurlyeq",!0),W("math",$,Q,"\u22df","\\curlyeqsucc",!0),W("math",$,Q,"\u227f","\\succsim",!0),W("math",$,Q,"\u2ab8","\\succapprox",!0),W("math",$,Q,"\u22b3","\\vartriangleright"),W("math",$,Q,"\u22b5","\\trianglerighteq"),W("math",$,Q,"\u22a9","\\Vdash",!0),W("math",$,Q,"\u2223","\\shortmid"),W("math",$,Q,"\u2225","\\shortparallel"),W("math",$,Q,"\u226c","\\between",!0),W("math",$,Q,"\u22d4","\\pitchfork",!0),W("math",$,Q,"\u221d","\\varpropto"),W("math",$,Q,"\u25c0","\\blacktriangleleft"),W("math",$,Q,"\u2234","\\therefore",!0),W("math",$,Q,"\u220d","\\backepsilon"),W("math",$,Q,"\u25b6","\\blacktriangleright"),W("math",$,Q,"\u2235","\\because",!0),W("math",$,Q,"\u22d8","\\llless"),W("math",$,Q,"\u22d9","\\gggtr"),W("math",$,Z,"\u22b2","\\lhd"),W("math",$,Z,"\u22b3","\\rhd"),W("math",$,Q,"\u2242","\\eqsim",!0),W("math",j,Q,"\u22c8","\\Join"),W("math",$,Q,"\u2251","\\Doteq",!0),W("math",$,Z,"\u2214","\\dotplus",!0),W("math",$,Z,"\u2216","\\smallsetminus"),W("math",$,Z,"\u22d2","\\Cap",!0),W("math",$,Z,"\u22d3","\\Cup",!0),W("math",$,Z,"\u2a5e","\\doublebarwedge",!0),W("math",$,Z,"\u229f","\\boxminus",!0),W("math",$,Z,"\u229e","\\boxplus",!0),W("math",$,Z,"\u22c7","\\divideontimes",!0),W("math",$,Z,"\u22c9","\\ltimes",!0),W("math",$,Z,"\u22ca","\\rtimes",!0),W("math",$,Z,"\u22cb","\\leftthreetimes",!0),W("math",$,Z,"\u22cc","\\rightthreetimes",!0),W("math",$,Z,"\u22cf","\\curlywedge",!0),W("math",$,Z,"\u22ce","\\curlyvee",!0),W("math",$,Z,"\u229d","\\circleddash",!0),W("math",$,Z,"\u229b","\\circledast",!0),W("math",$,Z,"\u22c5","\\centerdot"),W("math",$,Z,"\u22ba","\\intercal",!0),W("math",$,Z,"\u22d2","\\doublecap"),W("math",$,Z,"\u22d3","\\doublecup"),W("math",$,Z,"\u22a0","\\boxtimes",!0),W("math",$,Q,"\u21e2","\\dashrightarrow",!0),W("math",$,Q,"\u21e0","\\dashleftarrow",!0),W("math",$,Q,"\u21c7","\\leftleftarrows",!0),W("math",$,Q,"\u21c6","\\leftrightarrows",!0),W("math",$,Q,"\u21da","\\Lleftarrow",!0),W("math",$,Q,"\u219e","\\twoheadleftarrow",!0),W("math",$,Q,"\u21a2","\\leftarrowtail",!0),W("math",$,Q,"\u21ab","\\looparrowleft",!0),W("math",$,Q,"\u21cb","\\leftrightharpoons",!0),W("math",$,Q,"\u21b6","\\curvearrowleft",!0),W("math",$,Q,"\u21ba","\\circlearrowleft",!0),W("math",$,Q,"\u21b0","\\Lsh",!0),W("math",$,Q,"\u21c8","\\upuparrows",!0),W("math",$,Q,"\u21bf","\\upharpoonleft",!0),W("math",$,Q,"\u21c3","\\downharpoonleft",!0),W("math",$,Q,"\u22b8","\\multimap",!0),W("math",$,Q,"\u21ad","\\leftrightsquigarrow",!0),W("math",$,Q,"\u21c9","\\rightrightarrows",!0),W("math",$,Q,"\u21c4","\\rightleftarrows",!0),W("math",$,Q,"\u21a0","\\twoheadrightarrow",!0),W("math",$,Q,"\u21a3","\\rightarrowtail",!0),W("math",$,Q,"\u21ac","\\looparrowright",!0),W("math",$,Q,"\u21b7","\\curvearrowright",!0),W("math",$,Q,"\u21bb","\\circlearrowright",!0),W("math",$,Q,"\u21b1","\\Rsh",!0),W("math",$,Q,"\u21ca","\\downdownarrows",!0),W("math",$,Q,"\u21be","\\upharpoonright",!0),W("math",$,Q,"\u21c2","\\downharpoonright",!0),W("math",$,Q,"\u21dd","\\rightsquigarrow",!0),W("math",$,Q,"\u21dd","\\leadsto"),W("math",$,Q,"\u21db","\\Rrightarrow",!0),W("math",$,Q,"\u21be","\\restriction"),W("math",j,"textord","\u2018","`"),W("math",j,"textord","$","\\$"),W("text",j,"textord","$","\\$"),W("text",j,"textord","$","\\textdollar"),W("math",j,"textord","%","\\%"),W("text",j,"textord","%","\\%"),W("math",j,"textord","_","\\_"),W("text",j,"textord","_","\\_"),W("text",j,"textord","_","\\textunderscore"),W("math",j,"textord","\u2220","\\angle",!0),W("math",j,"textord","\u221e","\\infty",!0),W("math",j,"textord","\u2032","\\prime"),W("math",j,"textord","\u25b3","\\triangle"),W("math",j,"textord","\u0393","\\Gamma",!0),W("math",j,"textord","\u0394","\\Delta",!0),W("math",j,"textord","\u0398","\\Theta",!0),W("math",j,"textord","\u039b","\\Lambda",!0),W("math",j,"textord","\u039e","\\Xi",!0),W("math",j,"textord","\u03a0","\\Pi",!0),W("math",j,"textord","\u03a3","\\Sigma",!0),W("math",j,"textord","\u03a5","\\Upsilon",!0),W("math",j,"textord","\u03a6","\\Phi",!0),W("math",j,"textord","\u03a8","\\Psi",!0),W("math",j,"textord","\u03a9","\\Omega",!0),W("math",j,"textord","A","\u0391"),W("math",j,"textord","B","\u0392"),W("math",j,"textord","E","\u0395"),W("math",j,"textord","Z","\u0396"),W("math",j,"textord","H","\u0397"),W("math",j,"textord","I","\u0399"),W("math",j,"textord","K","\u039a"),W("math",j,"textord","M","\u039c"),W("math",j,"textord","N","\u039d"),W("math",j,"textord","O","\u039f"),W("math",j,"textord","P","\u03a1"),W("math",j,"textord","T","\u03a4"),W("math",j,"textord","X","\u03a7"),W("math",j,"textord","\xac","\\neg",!0),W("math",j,"textord","\xac","\\lnot"),W("math",j,"textord","\u22a4","\\top"),W("math",j,"textord","\u22a5","\\bot"),W("math",j,"textord","\u2205","\\emptyset"),W("math",$,"textord","\u2205","\\varnothing"),W("math",j,K,"\u03b1","\\alpha",!0),W("math",j,K,"\u03b2","\\beta",!0),W("math",j,K,"\u03b3","\\gamma",!0),W("math",j,K,"\u03b4","\\delta",!0),W("math",j,K,"\u03f5","\\epsilon",!0),W("math",j,K,"\u03b6","\\zeta",!0),W("math",j,K,"\u03b7","\\eta",!0),W("math",j,K,"\u03b8","\\theta",!0),W("math",j,K,"\u03b9","\\iota",!0),W("math",j,K,"\u03ba","\\kappa",!0),W("math",j,K,"\u03bb","\\lambda",!0),W("math",j,K,"\u03bc","\\mu",!0),W("math",j,K,"\u03bd","\\nu",!0),W("math",j,K,"\u03be","\\xi",!0),W("math",j,K,"\u03bf","\\omicron",!0),W("math",j,K,"\u03c0","\\pi",!0),W("math",j,K,"\u03c1","\\rho",!0),W("math",j,K,"\u03c3","\\sigma",!0),W("math",j,K,"\u03c4","\\tau",!0),W("math",j,K,"\u03c5","\\upsilon",!0),W("math",j,K,"\u03d5","\\phi",!0),W("math",j,K,"\u03c7","\\chi",!0),W("math",j,K,"\u03c8","\\psi",!0),W("math",j,K,"\u03c9","\\omega",!0),W("math",j,K,"\u03b5","\\varepsilon",!0),W("math",j,K,"\u03d1","\\vartheta",!0),W("math",j,K,"\u03d6","\\varpi",!0),W("math",j,K,"\u03f1","\\varrho",!0),W("math",j,K,"\u03c2","\\varsigma",!0),W("math",j,K,"\u03c6","\\varphi",!0),W("math",j,Z,"\u2217","*"),W("math",j,Z,"+","+"),W("math",j,Z,"\u2212","-"),W("math",j,Z,"\u22c5","\\cdot",!0),W("math",j,Z,"\u2218","\\circ"),W("math",j,Z,"\xf7","\\div",!0),W("math",j,Z,"\xb1","\\pm",!0),W("math",j,Z,"\xd7","\\times",!0),W("math",j,Z,"\u2229","\\cap",!0),W("math",j,Z,"\u222a","\\cup",!0),W("math",j,Z,"\u2216","\\setminus"),W("math",j,Z,"\u2227","\\land"),W("math",j,Z,"\u2228","\\lor"),W("math",j,Z,"\u2227","\\wedge",!0),W("math",j,Z,"\u2228","\\vee",!0),W("math",j,"textord","\u221a","\\surd"),W("math",j,"open","(","("),W("math",j,"open","[","["),W("math",j,"open","\u27e8","\\langle",!0),W("math",j,"open","\u2223","\\lvert"),W("math",j,"open","\u2225","\\lVert"),W("math",j,"close",")",")"),W("math",j,"close","]","]"),W("math",j,"close","?","?"),W("math",j,"close","!","!"),W("math",j,"close","\u27e9","\\rangle",!0),W("math",j,"close","\u2223","\\rvert"),W("math",j,"close","\u2225","\\rVert"),W("math",j,Q,"=","="),W("math",j,Q,"<","<"),W("math",j,Q,">",">"),W("math",j,Q,":",":"),W("math",j,Q,"\u2248","\\approx",!0),W("math",j,Q,"\u2245","\\cong",!0),W("math",j,Q,"\u2265","\\ge"),W("math",j,Q,"\u2265","\\geq",!0),W("math",j,Q,"\u2190","\\gets"),W("math",j,Q,">","\\gt"),W("math",j,Q,"\u2208","\\in",!0),W("math",j,Q,"\ue020","\\@not"),W("math",j,Q,"\u2282","\\subset",!0),W("math",j,Q,"\u2283","\\supset",!0),W("math",j,Q,"\u2286","\\subseteq",!0),W("math",j,Q,"\u2287","\\supseteq",!0),W("math",$,Q,"\u2288","\\nsubseteq",!0),W("math",$,Q,"\u2289","\\nsupseteq",!0),W("math",j,Q,"\u22a8","\\models"),W("math",j,Q,"\u2190","\\leftarrow",!0),W("math",j,Q,"\u2264","\\le"),W("math",j,Q,"\u2264","\\leq",!0),W("math",j,Q,"<","\\lt"),W("math",j,Q,"\u2192","\\rightarrow",!0),W("math",j,Q,"\u2192","\\to"),W("math",$,Q,"\u2271","\\ngeq",!0),W("math",$,Q,"\u2270","\\nleq",!0),W("math",j,tt,"\xa0","\\ "),W("math",j,tt,"\xa0","~"),W("math",j,tt,"\xa0","\\space"),W("math",j,tt,"\xa0","\\nobreakspace"),W("text",j,tt,"\xa0","\\ "),W("text",j,tt,"\xa0","~"),W("text",j,tt,"\xa0","\\space"),W("text",j,tt,"\xa0","\\nobreakspace"),W("math",j,tt,null,"\\nobreak"),W("math",j,tt,null,"\\allowbreak"),W("math",j,"punct",",",","),W("math",j,"punct",";",";"),W("math",$,Z,"\u22bc","\\barwedge",!0),W("math",$,Z,"\u22bb","\\veebar",!0),W("math",j,Z,"\u2299","\\odot",!0),W("math",j,Z,"\u2295","\\oplus",!0),W("math",j,Z,"\u2297","\\otimes",!0),W("math",j,"textord","\u2202","\\partial",!0),W("math",j,Z,"\u2298","\\oslash",!0),W("math",$,Z,"\u229a","\\circledcirc",!0),W("math",$,Z,"\u22a1","\\boxdot",!0),W("math",j,Z,"\u25b3","\\bigtriangleup"),W("math",j,Z,"\u25bd","\\bigtriangledown"),W("math",j,Z,"\u2020","\\dagger"),W("math",j,Z,"\u22c4","\\diamond"),W("math",j,Z,"\u22c6","\\star"),W("math",j,Z,"\u25c3","\\triangleleft"),W("math",j,Z,"\u25b9","\\triangleright"),W("math",j,"open","{","\\{"),W("text",j,"textord","{","\\{"),W("text",j,"textord","{","\\textbraceleft"),W("math",j,"close","}","\\}"),W("text",j,"textord","}","\\}"),W("text",j,"textord","}","\\textbraceright"),W("math",j,"open","{","\\lbrace"),W("math",j,"close","}","\\rbrace"),W("math",j,"open","[","\\lbrack"),W("text",j,"textord","[","\\lbrack"),W("math",j,"close","]","\\rbrack"),W("text",j,"textord","]","\\rbrack"),W("math",j,"open","(","\\lparen"),W("math",j,"close",")","\\rparen"),W("text",j,"textord","<","\\textless"),W("text",j,"textord",">","\\textgreater"),W("math",j,"open","\u230a","\\lfloor",!0),W("math",j,"close","\u230b","\\rfloor",!0),W("math",j,"open","\u2308","\\lceil",!0),W("math",j,"close","\u2309","\\rceil",!0),W("math",j,"textord","\\","\\backslash"),W("math",j,"textord","\u2223","|"),W("math",j,"textord","\u2223","\\vert"),W("text",j,"textord","|","\\textbar"),W("math",j,"textord","\u2225","\\|"),W("math",j,"textord","\u2225","\\Vert"),W("text",j,"textord","\u2225","\\textbardbl"),W("text",j,"textord","~","\\textasciitilde"),W("text",j,"textord","\\","\\textbackslash"),W("text",j,"textord","^","\\textasciicircum"),W("math",j,Q,"\u2191","\\uparrow",!0),W("math",j,Q,"\u21d1","\\Uparrow",!0),W("math",j,Q,"\u2193","\\downarrow",!0),W("math",j,Q,"\u21d3","\\Downarrow",!0),W("math",j,Q,"\u2195","\\updownarrow",!0),W("math",j,Q,"\u21d5","\\Updownarrow",!0),W("math",j,J,"\u2210","\\coprod"),W("math",j,J,"\u22c1","\\bigvee"),W("math",j,J,"\u22c0","\\bigwedge"),W("math",j,J,"\u2a04","\\biguplus"),W("math",j,J,"\u22c2","\\bigcap"),W("math",j,J,"\u22c3","\\bigcup"),W("math",j,J,"\u222b","\\int"),W("math",j,J,"\u222b","\\intop"),W("math",j,J,"\u222c","\\iint"),W("math",j,J,"\u222d","\\iiint"),W("math",j,J,"\u220f","\\prod"),W("math",j,J,"\u2211","\\sum"),W("math",j,J,"\u2a02","\\bigotimes"),W("math",j,J,"\u2a01","\\bigoplus"),W("math",j,J,"\u2a00","\\bigodot"),W("math",j,J,"\u222e","\\oint"),W("math",j,J,"\u222f","\\oiint"),W("math",j,J,"\u2230","\\oiiint"),W("math",j,J,"\u2a06","\\bigsqcup"),W("math",j,J,"\u222b","\\smallint"),W("text",j,"inner","\u2026","\\textellipsis"),W("math",j,"inner","\u2026","\\mathellipsis"),W("text",j,"inner","\u2026","\\ldots",!0),W("math",j,"inner","\u2026","\\ldots",!0),W("math",j,"inner","\u22ef","\\@cdots",!0),W("math",j,"inner","\u22f1","\\ddots",!0),W("math",j,"textord","\u22ee","\\varvdots"),W("math",j,"accent-token","\u02ca","\\acute"),W("math",j,"accent-token","\u02cb","\\grave"),W("math",j,"accent-token","\xa8","\\ddot"),W("math",j,"accent-token","~","\\tilde"),W("math",j,"accent-token","\u02c9","\\bar"),W("math",j,"accent-token","\u02d8","\\breve"),W("math",j,"accent-token","\u02c7","\\check"),W("math",j,"accent-token","^","\\hat"),W("math",j,"accent-token","\u20d7","\\vec"),W("math",j,"accent-token","\u02d9","\\dot"),W("math",j,"accent-token","\u02da","\\mathring"),W("math",j,K,"\u0131","\\imath",!0),W("math",j,K,"\u0237","\\jmath",!0),W("text",j,"textord","\u0131","\\i",!0),W("text",j,"textord","\u0237","\\j",!0),W("text",j,"textord","\xdf","\\ss",!0),W("text",j,"textord","\xe6","\\ae",!0),W("text",j,"textord","\xe6","\\ae",!0),W("text",j,"textord","\u0153","\\oe",!0),W("text",j,"textord","\xf8","\\o",!0),W("text",j,"textord","\xc6","\\AE",!0),W("text",j,"textord","\u0152","\\OE",!0),W("text",j,"textord","\xd8","\\O",!0),W("text",j,"accent-token","\u02ca","\\'"),W("text",j,"accent-token","\u02cb","\\`"),W("text",j,"accent-token","\u02c6","\\^"),W("text",j,"accent-token","\u02dc","\\~"),W("text",j,"accent-token","\u02c9","\\="),W("text",j,"accent-token","\u02d8","\\u"),W("text",j,"accent-token","\u02d9","\\."),W("text",j,"accent-token","\u02da","\\r"),W("text",j,"accent-token","\u02c7","\\v"),W("text",j,"accent-token","\xa8",'\\"'),W("text",j,"accent-token","\u02dd","\\H"),W("text",j,"accent-token","\u25ef","\\textcircled");var et={"--":!0,"---":!0,"``":!0,"''":!0};W("text",j,"textord","\u2013","--"),W("text",j,"textord","\u2013","\\textendash"),W("text",j,"textord","\u2014","---"),W("text",j,"textord","\u2014","\\textemdash"),W("text",j,"textord","\u2018","`"),W("text",j,"textord","\u2018","\\textquoteleft"),W("text",j,"textord","\u2019","'"),W("text",j,"textord","\u2019","\\textquoteright"),W("text",j,"textord","\u201c","``"),W("text",j,"textord","\u201c","\\textquotedblleft"),W("text",j,"textord","\u201d","''"),W("text",j,"textord","\u201d","\\textquotedblright"),W("math",j,"textord","\xb0","\\degree",!0),W("text",j,"textord","\xb0","\\degree"),W("text",j,"textord","\xb0","\\textdegree",!0),W("math",j,K,"\xa3","\\pounds"),W("math",j,K,"\xa3","\\mathsterling",!0),W("text",j,K,"\xa3","\\pounds"),W("text",j,K,"\xa3","\\textsterling",!0),W("math",$,"textord","\u2720","\\maltese"),W("text",$,"textord","\u2720","\\maltese"),W("text",j,tt,"\xa0","\\ "),W("text",j,tt,"\xa0"," "),W("text",j,tt,"\xa0","~");for(var rt=0;rt<'0123456789/@."'.length;rt++){var nt='0123456789/@."'.charAt(rt);W("math",j,"textord",nt,nt)}for(var at=0;at<'0123456789!@*()-=+[]<>|";:?/.,'.length;at++){var it='0123456789!@*()-=+[]<>|";:?/.,'.charAt(at);W("text",j,"textord",it,it)}for(var ot="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",st=0;st<ot.length;st++){var ht=ot.charAt(st);W("math",j,K,ht,ht),W("text",j,"textord",ht,ht)}W("math",$,"textord","C","\u2102"),W("text",$,"textord","C","\u2102"),W("math",$,"textord","H","\u210d"),W("text",$,"textord","H","\u210d"),W("math",$,"textord","N","\u2115"),W("text",$,"textord","N","\u2115"),W("math",$,"textord","P","\u2119"),W("text",$,"textord","P","\u2119"),W("math",$,"textord","Q","\u211a"),W("text",$,"textord","Q","\u211a"),W("math",$,"textord","R","\u211d"),W("text",$,"textord","R","\u211d"),W("math",$,"textord","Z","\u2124"),W("text",$,"textord","Z","\u2124"),W("math",j,K,"h","\u210e"),W("text",j,K,"h","\u210e");for(var lt="",mt=0;mt<ot.length;mt++){var ct=ot.charAt(mt);W("math",j,K,ct,lt=String.fromCharCode(55349,56320+mt)),W("text",j,"textord",ct,lt),W("math",j,K,ct,lt=String.fromCharCode(55349,56372+mt)),W("text",j,"textord",ct,lt),W("math",j,K,ct,lt=String.fromCharCode(55349,56424+mt)),W("text",j,"textord",ct,lt),W("math",j,K,ct,lt=String.fromCharCode(55349,56580+mt)),W("text",j,"textord",ct,lt),W("math",j,K,ct,lt=String.fromCharCode(55349,56736+mt)),W("text",j,"textord",ct,lt),W("math",j,K,ct,lt=String.fromCharCode(55349,56788+mt)),W("text",j,"textord",ct,lt),W("math",j,K,ct,lt=String.fromCharCode(55349,56840+mt)),W("text",j,"textord",ct,lt),W("math",j,K,ct,lt=String.fromCharCode(55349,56944+mt)),W("text",j,"textord",ct,lt),mt<26&&(W("math",j,K,ct,lt=String.fromCharCode(55349,56632+mt)),W("text",j,"textord",ct,lt),W("math",j,K,ct,lt=String.fromCharCode(55349,56476+mt)),W("text",j,"textord",ct,lt))}W("math",j,K,"k",lt=String.fromCharCode(55349,56668)),W("text",j,"textord","k",lt);for(var ut=0;ut<10;ut++){var dt=ut.toString();W("math",j,K,dt,lt=String.fromCharCode(55349,57294+ut)),W("text",j,"textord",dt,lt),W("math",j,K,dt,lt=String.fromCharCode(55349,57314+ut)),W("text",j,"textord",dt,lt),W("math",j,K,dt,lt=String.fromCharCode(55349,57324+ut)),W("text",j,"textord",dt,lt),W("math",j,K,dt,lt=String.fromCharCode(55349,57334+ut)),W("text",j,"textord",dt,lt)}for(var pt=0;pt<"\xc7\xd0\xde\xe7\xfe".length;pt++){var ft="\xc7\xd0\xde\xe7\xfe".charAt(pt);W("math",j,K,ft,ft),W("text",j,"textord",ft,ft)}W("text",j,"textord","\xf0","\xf0"),W("text",j,"textord","\u2013","\u2013"),W("text",j,"textord","\u2014","\u2014"),W("text",j,"textord","\u2018","\u2018"),W("text",j,"textord","\u2019","\u2019"),W("text",j,"textord","\u201c","\u201c"),W("text",j,"textord","\u201d","\u201d");var gt=[["mathbf","textbf","Main-Bold"],["mathbf","textbf","Main-Bold"],["mathdefault","textit","Math-Italic"],["mathdefault","textit","Math-Italic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["mathscr","textscr","Script-Regular"],["","",""],["","",""],["","",""],["mathfrak","textfrak","Fraktur-Regular"],["mathfrak","textfrak","Fraktur-Regular"],["mathbb","textbb","AMS-Regular"],["mathbb","textbb","AMS-Regular"],["","",""],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathitsf","textitsf","SansSerif-Italic"],["mathitsf","textitsf","SansSerif-Italic"],["","",""],["","",""],["mathtt","texttt","Typewriter-Regular"],["mathtt","texttt","Typewriter-Regular"]],xt=[["mathbf","textbf","Main-Bold"],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathtt","texttt","Typewriter-Regular"]],vt=[[1,1,1],[2,1,1],[3,1,1],[4,2,1],[5,2,1],[6,3,1],[7,4,2],[8,6,3],[9,7,6],[10,8,7],[11,10,9]],yt=[.5,.6,.7,.8,.9,1,1.2,1.44,1.728,2.074,2.488],bt=function(t,e){return e.size<2?t:vt[t-1][e.size-1]},wt=function(){function t(e){this.style=void 0,this.color=void 0,this.size=void 0,this.textSize=void 0,this.phantom=void 0,this.font=void 0,this.fontFamily=void 0,this.fontWeight=void 0,this.fontShape=void 0,this.sizeMultiplier=void 0,this.maxSize=void 0,this._fontMetrics=void 0,this.style=e.style,this.color=e.color,this.size=e.size||t.BASESIZE,this.textSize=e.textSize||this.size,this.phantom=!!e.phantom,this.font=e.font||"",this.fontFamily=e.fontFamily||"",this.fontWeight=e.fontWeight||"",this.fontShape=e.fontShape||"",this.sizeMultiplier=yt[this.size-1],this.maxSize=e.maxSize,this._fontMetrics=void 0}var e=t.prototype;return e.extend=function(e){var r={style:this.style,size:this.size,textSize:this.textSize,color:this.color,phantom:this.phantom,font:this.font,fontFamily:this.fontFamily,fontWeight:this.fontWeight,fontShape:this.fontShape,maxSize:this.maxSize};for(var n in e)e.hasOwnProperty(n)&&(r[n]=e[n]);return new t(r)},e.havingStyle=function(t){return this.style===t?this:this.extend({style:t,size:bt(this.textSize,t)})},e.havingCrampedStyle=function(){return this.havingStyle(this.style.cramp())},e.havingSize=function(t){return this.size===t&&this.textSize===t?this:this.extend({style:this.style.text(),size:t,textSize:t,sizeMultiplier:yt[t-1]})},e.havingBaseStyle=function(e){e=e||this.style.text();var r=bt(t.BASESIZE,e);return this.size===r&&this.textSize===t.BASESIZE&&this.style===e?this:this.extend({style:e,size:r})},e.havingBaseSizing=function(){var t;switch(this.style.id){case 4:case 5:t=3;break;case 6:case 7:t=1;break;default:t=6}return this.extend({style:this.style.text(),size:t})},e.withColor=function(t){return this.extend({color:t})},e.withPhantom=function(){return this.extend({phantom:!0})},e.withFont=function(t){return this.extend({font:t})},e.withTextFontFamily=function(t){return this.extend({fontFamily:t,font:""})},e.withTextFontWeight=function(t){return this.extend({fontWeight:t,font:""})},e.withTextFontShape=function(t){return this.extend({fontShape:t,font:""})},e.sizingClasses=function(t){return t.size!==this.size?["sizing","reset-size"+t.size,"size"+this.size]:[]},e.baseSizingClasses=function(){return this.size!==t.BASESIZE?["sizing","reset-size"+this.size,"size"+t.BASESIZE]:[]},e.fontMetrics=function(){return this._fontMetrics||(this._fontMetrics=function(t){var e;if(!U[e=t>=5?0:t>=3?1:2]){var r=U[e]={cssEmPerMu:P.quad[e]/18};for(var n in P)P.hasOwnProperty(n)&&(r[n]=P[n][e])}return U[e]}(this.size)),this._fontMetrics},e.getColor=function(){return this.phantom?"transparent":null!=this.color&&t.colorMap.hasOwnProperty(this.color)?t.colorMap[this.color]:this.color},t}();wt.BASESIZE=6,wt.colorMap={"katex-blue":"#6495ed","katex-orange":"#ffa500","katex-pink":"#ff00af","katex-red":"#df0030","katex-green":"#28ae7b","katex-gray":"gray","katex-purple":"#9d38bd","katex-blueA":"#ccfaff","katex-blueB":"#80f6ff","katex-blueC":"#63d9ea","katex-blueD":"#11accd","katex-blueE":"#0c7f99","katex-tealA":"#94fff5","katex-tealB":"#26edd5","katex-tealC":"#01d1c1","katex-tealD":"#01a995","katex-tealE":"#208170","katex-greenA":"#b6ffb0","katex-greenB":"#8af281","katex-greenC":"#74cf70","katex-greenD":"#1fab54","katex-greenE":"#0d923f","katex-goldA":"#ffd0a9","katex-goldB":"#ffbb71","katex-goldC":"#ff9c39","katex-goldD":"#e07d10","katex-goldE":"#a75a05","katex-redA":"#fca9a9","katex-redB":"#ff8482","katex-redC":"#f9685d","katex-redD":"#e84d39","katex-redE":"#bc2612","katex-maroonA":"#ffbde0","katex-maroonB":"#ff92c6","katex-maroonC":"#ed5fa6","katex-maroonD":"#ca337c","katex-maroonE":"#9e034e","katex-purpleA":"#ddd7ff","katex-purpleB":"#c6b9fc","katex-purpleC":"#aa87ff","katex-purpleD":"#7854ab","katex-purpleE":"#543b78","katex-mintA":"#f5f9e8","katex-mintB":"#edf2df","katex-mintC":"#e0e5cc","katex-grayA":"#f6f7f7","katex-grayB":"#f0f1f2","katex-grayC":"#e3e5e6","katex-grayD":"#d6d8da","katex-grayE":"#babec2","katex-grayF":"#888d93","katex-grayG":"#626569","katex-grayH":"#3b3e40","katex-grayI":"#21242c","katex-kaBlue":"#314453","katex-kaGreen":"#71B307"};var kt=wt,St={pt:1,mm:7227/2540,cm:7227/254,in:72.27,bp:1.00375,pc:12,dd:1238/1157,cc:14856/1157,nd:685/642,nc:1370/107,sp:1/65536,px:1.00375},zt={ex:!0,em:!0,mu:!0},Mt=function(t,e){var r;if(t.unit in St)r=St[t.unit]/e.fontMetrics().ptPerEm/e.sizeMultiplier;else if("mu"===t.unit)r=e.fontMetrics().cssEmPerMu;else{var n;if(n=e.style.isTight()?e.havingStyle(e.style.text()):e,"ex"===t.unit)r=n.fontMetrics().xHeight;else{if("em"!==t.unit)throw new o("Invalid unit: '"+t.unit+"'");r=n.fontMetrics().quad}n!==e&&(r*=n.sizeMultiplier/e.sizeMultiplier)}return Math.min(t.number*r,e.maxSize)},Tt=["\\imath","\u0131","\\jmath","\u0237","\\pounds","\\mathsterling","\\textsterling","\xa3"],At=function(t,e,r){return _[r][t]&&_[r][t].replace&&(t=_[r][t].replace),{value:t,metrics:V(t,e,r)}},Bt=function(t,e,r,n,a){var i,o=At(t,e,r),s=o.metrics;if(t=o.value,s){var h=s.italic;("text"===r||n&&"mathit"===n.font)&&(h=0),i=new I(t,s.height,s.depth,h,s.skew,s.width,a)}else"undefined"!=typeof console&&console.warn("No character metrics for '"+t+"' in style '"+e+"'"),i=new I(t,0,0,0,0,0,a);if(n){i.maxFontSize=n.sizeMultiplier,n.style.isTight()&&i.classes.push("mtight");var l=n.getColor();l&&(i.style.color=l)}return i},Ct=function(t,e){if(A(t.classes)!==A(e.classes)||t.skew!==e.skew||t.maxFontSize!==e.maxFontSize)return!1;for(var r in t.style)if(t.style.hasOwnProperty(r)&&t.style[r]!==e.style[r])return!1;for(var n in e.style)if(e.style.hasOwnProperty(n)&&t.style[n]!==e.style[n])return!1;return!0},Nt=function(t){for(var e=0,r=0,n=0,a=0;a<t.children.length;a++){var i=t.children[a];i.height>e&&(e=i.height),i.depth>r&&(r=i.depth),i.maxFontSize>n&&(n=i.maxFontSize)}t.height=e,t.depth=r,t.maxFontSize=n},qt=function(t,e,r,n){var a=new q(t,e,r,n);return Nt(a),a},Et=function(t,e,r,n){return new q(t,e,r,n)},Ot=function(t){var e=new T(t);return Nt(e),e},It=function(t,e,r){var n="";switch(t){case"amsrm":n="AMS";break;case"textrm":n="Main";break;case"textsf":n="SansSerif";break;case"texttt":n="Typewriter";break;default:n=t}return n+"-"+("textbf"===e&&"textit"===r?"BoldItalic":"textbf"===e?"Bold":"textit"===e?"Italic":"Regular")},Rt={mathbf:{variant:"bold",fontName:"Main-Bold"},mathrm:{variant:"normal",fontName:"Main-Regular"},textit:{variant:"italic",fontName:"Main-Italic"},mathit:{variant:"italic",fontName:"Main-Italic"},mathbb:{variant:"double-struck",fontName:"AMS-Regular"},mathcal:{variant:"script",fontName:"Caligraphic-Regular"},mathfrak:{variant:"fraktur",fontName:"Fraktur-Regular"},mathscr:{variant:"script",fontName:"Script-Regular"},mathsf:{variant:"sans-serif",fontName:"SansSerif-Regular"},mathtt:{variant:"monospace",fontName:"Typewriter-Regular"}},Lt={vec:["vec",.471,.714],oiintSize1:["oiintSize1",.957,.499],oiintSize2:["oiintSize2",1.472,.659],oiiintSize1:["oiiintSize1",1.304,.499],oiiintSize2:["oiiintSize2",1.98,.659]},Dt={fontMap:Rt,makeSymbol:Bt,mathsym:function(t,e,r,n){return void 0===n&&(n=[]),r&&r.font&&"boldsymbol"===r.font&&At(t,"Main-Bold",e).metrics?Bt(t,"Main-Bold",e,r,n.concat(["mathbf"])):"\\"===t||"main"===_[e][t].font?Bt(t,"Main-Regular",e,r,n):Bt(t,"AMS-Regular",e,r,n.concat(["amsrm"]))},makeSpan:qt,makeSvgSpan:Et,makeLineSpan:function(t,e,r){var n=qt([t],[],e);return n.height=r||e.fontMetrics().defaultRuleThickness,n.style.borderBottomWidth=n.height+"em",n.maxFontSize=1,n},makeAnchor:function(t,e,r,n){var a=new E(t,e,r,n);return Nt(a),a},makeFragment:Ot,wrapFragment:function(t,e){return t instanceof T?qt([],[t],e):t},makeVList:function(t,e){for(var r=function(t){if("individualShift"===t.positionType){for(var e=t.children,r=[e[0]],n=-e[0].shift-e[0].elem.depth,a=n,i=1;i<e.length;i++){var o=-e[i].shift-a-e[i].elem.depth,s=o-(e[i-1].elem.height+e[i-1].elem.depth);a+=o,r.push({type:"kern",size:s}),r.push(e[i])}return{children:r,depth:n}}var h;if("top"===t.positionType){for(var l=t.positionData,m=0;m<t.children.length;m++){var c=t.children[m];l-="kern"===c.type?c.size:c.elem.height+c.elem.depth}h=l}else if("bottom"===t.positionType)h=-t.positionData;else{var u=t.children[0];if("elem"!==u.type)throw new Error('First child must have type "elem".');if("shift"===t.positionType)h=-u.elem.depth-t.positionData;else{if("firstBaseline"!==t.positionType)throw new Error("Invalid positionType "+t.positionType+".");h=-u.elem.depth}}return{children:t.children,depth:h}}(t),n=r.children,a=r.depth,i=0,o=0;o<n.length;o++){var s=n[o];if("elem"===s.type){var h=s.elem;i=Math.max(i,h.maxFontSize,h.height)}}i+=2;var l=qt(["pstrut"],[]);l.style.height=i+"em";for(var m=[],c=a,u=a,d=a,p=0;p<n.length;p++){var f=n[p];if("kern"===f.type)d+=f.size;else{var g=f.elem,x=f.wrapperClasses||[],v=f.wrapperStyle||{},y=qt(x,[l,g],void 0,v);y.style.top=-i-d-g.depth+"em",f.marginLeft&&(y.style.marginLeft=f.marginLeft),f.marginRight&&(y.style.marginRight=f.marginRight),m.push(y),d+=g.height+g.depth}c=Math.min(c,d),u=Math.max(u,d)}var b,w=qt(["vlist"],m);if(w.style.height=u+"em",c<0){var k=qt([],[]),S=qt(["vlist"],[k]);S.style.height=-c+"em";var z=qt(["vlist-s"],[new I("\u200b")]);b=[qt(["vlist-r"],[w,z]),qt(["vlist-r"],[S])]}else b=[qt(["vlist-r"],[w])];var M=qt(["vlist-t"],b);return 2===b.length&&M.classes.push("vlist-t2"),M.height=u,M.depth=-c,M},makeOrd:function(t,e,r){var n,a=t.mode,i=t.text,s=["mord"],h="math"===a||"text"===a&&e.font,l=h?e.font:e.fontFamily;if(55349===i.charCodeAt(0)){var m=function(t,e){var r=1024*(t.charCodeAt(0)-55296)+(t.charCodeAt(1)-56320)+65536,n="math"===e?0:1;if(119808<=r&&r<120484){var a=Math.floor((r-119808)/26);return[gt[a][2],gt[a][n]]}if(120782<=r&&r<=120831){var i=Math.floor((r-120782)/10);return[xt[i][2],xt[i][n]]}if(120485===r||120486===r)return[gt[0][2],gt[0][n]];if(120486<r&&r<120782)return["",""];throw new o("Unsupported character: "+t)}(i,a),u=m[0],d=m[1];return Bt(i,u,a,e,s.concat(d))}if(l){var p,f;if("boldsymbol"===l||"mathnormal"===l){var g="boldsymbol"===l?function(t,e,r,n){return At(t,"Math-BoldItalic",e).metrics?{fontName:"Math-BoldItalic",fontClass:"boldsymbol"}:{fontName:"Main-Bold",fontClass:"mathbf"}}(i,a):(n=i,c.contains(Tt,n)?{fontName:"Main-Italic",fontClass:"mathit"}:/[0-9]/.test(n.charAt(0))?{fontName:"Caligraphic-Regular",fontClass:"mathcal"}:{fontName:"Math-Italic",fontClass:"mathdefault"});p=g.fontName,f=[g.fontClass]}else c.contains(Tt,i)?(p="Main-Italic",f=["mathit"]):h?(p=Rt[l].fontName,f=[l]):(p=It(l,e.fontWeight,e.fontShape),f=[l,e.fontWeight,e.fontShape]);if(At(i,p,a).metrics)return Bt(i,p,a,e,s.concat(f));if(et.hasOwnProperty(i)&&"Typewriter"===p.substr(0,10)){for(var x=[],v=0;v<i.length;v++)x.push(Bt(i[v],p,a,e,s.concat(f)));return Ot(x)}}if("mathord"===r){var y=function(t,e,r,n){return/[0-9]/.test(t.charAt(0))||c.contains(Tt,t)?{fontName:"Main-Italic",fontClass:"mathit"}:{fontName:"Math-Italic",fontClass:"mathdefault"}}(i);return Bt(i,y.fontName,a,e,s.concat([y.fontClass]))}if("textord"===r){var b=_[a][i]&&_[a][i].font;if("ams"===b){var w=It("amsrm",e.fontWeight,e.fontShape);return Bt(i,w,a,e,s.concat("amsrm",e.fontWeight,e.fontShape))}if("main"!==b&&b){var k=It(b,e.fontWeight,e.fontShape);return Bt(i,k,a,e,s.concat(k,e.fontWeight,e.fontShape))}var S=It("textrm",e.fontWeight,e.fontShape);return Bt(i,S,a,e,s.concat(e.fontWeight,e.fontShape))}throw new Error("unexpected type: "+r+" in makeOrd")},makeGlue:function(t,e){var r=qt(["mspace"],[],e),n=Mt(t,e);return r.style.marginRight=n+"em",r},staticSvg:function(t,e){var r=Lt[t],n=r[0],a=r[1],i=r[2],o=new L(n),s=new R([o],{width:a+"em",height:i+"em",style:"width:"+a+"em",viewBox:"0 0 "+1e3*a+" "+1e3*i,preserveAspectRatio:"xMinYMin"}),h=Et(["overlay"],[s],e);return h.height=i,h.style.height=i+"em",h.style.width=a+"em",h},svgData:Lt,tryCombineChars:function(t){for(var e=0;e<t.length-1;e++){var r=t[e],n=t[e+1];r instanceof I&&n instanceof I&&Ct(r,n)&&(r.text+=n.text,r.height=Math.max(r.height,n.height),r.depth=Math.max(r.depth,n.depth),r.italic=n.italic,t.splice(e+1,1),e--)}return t}};function Ht(t,e){var r=Pt(t,e);if(!r)throw new Error("Expected node of type "+e+", but got "+(t?"node of type "+t.type:String(t)));return r}function Pt(t,e){return t&&t.type===e?t:null}function Ft(t,e){var r=function(t,e){return t&&"atom"===t.type&&t.family===e?t:null}(t,e);if(!r)throw new Error('Expected node of type "atom" and family "'+e+'", but got '+(t?"atom"===t.type?"atom of family "+t.family:"node of type "+t.type:String(t)));return r}function Vt(t){return t&&("atom"===t.type||X.hasOwnProperty(t.type))?t:null}var Ut={number:3,unit:"mu"},Gt={number:4,unit:"mu"},Xt={number:5,unit:"mu"},Yt={mord:{mop:Ut,mbin:Gt,mrel:Xt,minner:Ut},mop:{mord:Ut,mop:Ut,mrel:Xt,minner:Ut},mbin:{mord:Gt,mop:Gt,mopen:Gt,minner:Gt},mrel:{mord:Xt,mop:Xt,mopen:Xt,minner:Xt},mopen:{},mclose:{mop:Ut,mbin:Gt,mrel:Xt,minner:Ut},mpunct:{mord:Ut,mop:Ut,mrel:Xt,mopen:Ut,mclose:Ut,mpunct:Ut,minner:Ut},minner:{mord:Ut,mop:Ut,mbin:Gt,mrel:Xt,mopen:Ut,mpunct:Ut,minner:Ut}},_t={mord:{mop:Ut},mop:{mord:Ut,mop:Ut},mbin:{},mrel:{},mopen:{},mclose:{mop:Ut},mpunct:{},minner:{mop:Ut}},Wt={},jt={},$t={};function Zt(t){for(var e=t.type,r=(t.nodeType,t.names),n=t.props,a=t.handler,i=t.htmlBuilder,o=t.mathmlBuilder,s={type:e,numArgs:n.numArgs,argTypes:n.argTypes,greediness:void 0===n.greediness?1:n.greediness,allowedInText:!!n.allowedInText,allowedInMath:void 0===n.allowedInMath||n.allowedInMath,numOptionalArgs:n.numOptionalArgs||0,infix:!!n.infix,consumeMode:n.consumeMode,handler:a},h=0;h<r.length;++h)Wt[r[h]]=s;e&&(i&&(jt[e]=i),o&&($t[e]=o))}function Kt(t){Zt({type:t.type,names:[],props:{numArgs:0},handler:function(){throw new Error("Should never be called.")},htmlBuilder:t.htmlBuilder,mathmlBuilder:t.mathmlBuilder})}var Jt=function(t){var e=Pt(t,"ordgroup");return e?e.body:[t]},Qt=Dt.makeSpan,te=["leftmost","mbin","mopen","mrel","mop","mpunct"],ee=["rightmost","mrel","mclose","mpunct"],re={display:w.DISPLAY,text:w.TEXT,script:w.SCRIPT,scriptscript:w.SCRIPTSCRIPT},ne={mord:"mord",mop:"mop",mbin:"mbin",mrel:"mrel",mopen:"mopen",mclose:"mclose",mpunct:"mpunct",minner:"minner"},ae=function(t,e,r,n){void 0===n&&(n=[null,null]);for(var a=[],i=0;i<t.length;i++){var o=le(t[i],e);if(o instanceof T){var s=o.children;a.push.apply(a,s)}else a.push(o)}if(!r)return a;var h=e;if(1===t.length){var l=Pt(t[0],"sizing")||Pt(t[0],"styling");l&&("sizing"===l.type?h=e.havingSize(l.size):"styling"===l.type&&(h=e.havingStyle(re[l.style])))}var m=Qt([n[0]||"leftmost"],[],e),u=Qt([n[1]||"rightmost"],[],e);return ie(a,function(t,e){var r=e.classes[0],n=t.classes[0];"mbin"===r&&c.contains(ee,n)?e.classes[0]="mord":"mbin"===n&&c.contains(te,r)&&(t.classes[0]="mord")},{node:m},u),ie(a,function(t,e){var r=se(e),n=se(t),a=r&&n?t.hasClass("mtight")?_t[r][n]:Yt[r][n]:null;if(a)return Dt.makeGlue(a,h)},{node:m},u),a},ie=function t(e,r,n,a){a&&e.push(a);for(var i=0;i<e.length;i++){var o=e[i],s=oe(o);if(s)t(s.children,r,n);else if("mspace"!==o.classes[0]){var h=r(o,n.node);h&&(n.insertAfter?n.insertAfter(h):(e.unshift(h),i++)),n.node=o,n.insertAfter=function(t){return function(r){e.splice(t+1,0,r),i++}}(i)}}a&&e.pop()},oe=function(t){return t instanceof T||t instanceof E?t:null},se=function(t,e){return t?(e&&(t=function t(e,r){var n=oe(e);if(n){var a=n.children;if(a.length){if("right"===r)return t(a[a.length-1],"right");if("left"===r)return t(a[0],"left")}}return e}(t,e)),ne[t.classes[0]]||null):null},he=function(t,e){var r=["nulldelimiter"].concat(t.baseSizingClasses());return Qt(e.concat(r))},le=function(t,e,r){if(!t)return Qt();if(jt[t.type]){var n=jt[t.type](t,e);if(r&&e.size!==r.size){n=Qt(e.sizingClasses(r),[n],e);var a=e.sizeMultiplier/r.sizeMultiplier;n.height*=a,n.depth*=a}return n}throw new o("Got group of unknown type: '"+t.type+"'")};function me(t,e){var r=Qt(["base"],t,e),n=Qt(["strut"]);return n.style.height=r.height+r.depth+"em",n.style.verticalAlign=-r.depth+"em",r.children.unshift(n),r}function ce(t,e){var r=null;1===t.length&&"tag"===t[0].type&&(r=t[0].tag,t=t[0].body);for(var n,a=ae(t,e,!0),i=[],o=[],s=0;s<a.length;s++)if(o.push(a[s]),a[s].hasClass("mbin")||a[s].hasClass("mrel")||a[s].hasClass("allowbreak")){for(var h=!1;s<a.length-1&&a[s+1].hasClass("mspace")&&!a[s+1].hasClass("newline");)s++,o.push(a[s]),a[s].hasClass("nobreak")&&(h=!0);h||(i.push(me(o,e)),o=[])}else a[s].hasClass("newline")&&(o.pop(),o.length>0&&(i.push(me(o,e)),o=[]),i.push(a[s]));o.length>0&&i.push(me(o,e)),r&&((n=me(ae(r,e,!0))).classes=["tag"],i.push(n));var l=Qt(["katex-html"],i);if(l.setAttribute("aria-hidden","true"),n){var m=n.children[0];m.style.height=l.height+l.depth+"em",m.style.verticalAlign=-l.depth+"em"}return l}function ue(t){return new T(t)}var de=function(){function t(t,e){this.type=void 0,this.attributes=void 0,this.children=void 0,this.type=t,this.attributes={},this.children=e||[]}var e=t.prototype;return e.setAttribute=function(t,e){this.attributes[t]=e},e.getAttribute=function(t){return this.attributes[t]},e.toNode=function(){var t=document.createElementNS("http://www.w3.org/1998/Math/MathML",this.type);for(var e in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,e)&&t.setAttribute(e,this.attributes[e]);for(var r=0;r<this.children.length;r++)t.appendChild(this.children[r].toNode());return t},e.toMarkup=function(){var t="<"+this.type;for(var e in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,e)&&(t+=" "+e+'="',t+=c.escape(this.attributes[e]),t+='"');t+=">";for(var r=0;r<this.children.length;r++)t+=this.children[r].toMarkup();return t+="</"+this.type+">"},e.toText=function(){return this.children.map(function(t){return t.toText()}).join("")},t}(),pe=function(){function t(t){this.text=void 0,this.text=t}var e=t.prototype;return e.toNode=function(){return document.createTextNode(this.text)},e.toMarkup=function(){return c.escape(this.toText())},e.toText=function(){return this.text},t}(),fe={MathNode:de,TextNode:pe,SpaceNode:function(){function t(t){this.width=void 0,this.character=void 0,this.width=t,this.character=t>=.05555&&t<=.05556?"\u200a":t>=.1666&&t<=.1667?"\u2009":t>=.2222&&t<=.2223?"\u2005":t>=.2777&&t<=.2778?"\u2005\u200a":t>=-.05556&&t<=-.05555?"\u200a\u2063":t>=-.1667&&t<=-.1666?"\u2009\u2063":t>=-.2223&&t<=-.2222?"\u205f\u2063":t>=-.2778&&t<=-.2777?"\u2005\u2063":null}var e=t.prototype;return e.toNode=function(){if(this.character)return document.createTextNode(this.character);var t=document.createElementNS("http://www.w3.org/1998/Math/MathML","mspace");return t.setAttribute("width",this.width+"em"),t},e.toMarkup=function(){return this.character?"<mtext>"+this.character+"</mtext>":'<mspace width="'+this.width+'em"/>'},e.toText=function(){return this.character?this.character:" "},t}(),newDocumentFragment:ue},ge=function(t,e,r){return!_[e][t]||!_[e][t].replace||55349===t.charCodeAt(0)||et.hasOwnProperty(t)&&r&&(r.fontFamily&&"tt"===r.fontFamily.substr(4,2)||r.font&&"tt"===r.font.substr(4,2))||(t=_[e][t].replace),new fe.TextNode(t)},xe=function(t){return 1===t.length?t[0]:new fe.MathNode("mrow",t)},ve=function(t,e){if("texttt"===e.fontFamily)return"monospace";if("textsf"===e.fontFamily)return"textit"===e.fontShape&&"textbf"===e.fontWeight?"sans-serif-bold-italic":"textit"===e.fontShape?"sans-serif-italic":"textbf"===e.fontWeight?"bold-sans-serif":"sans-serif";if("textit"===e.fontShape&&"textbf"===e.fontWeight)return"bold-italic";if("textit"===e.fontShape)return"italic";if("textbf"===e.fontWeight)return"bold";var r=e.font;if(!r||"mathnormal"===r)return null;var n=t.mode;if("mathit"===r)return"italic";if("boldsymbol"===r)return"bold-italic";var a=t.text;return c.contains(["\\imath","\\jmath"],a)?null:(_[n][a]&&_[n][a].replace&&(a=_[n][a].replace),V(a,Dt.fontMap[r].fontName,n)?Dt.fontMap[r].variant:null)},ye=function(t,e){for(var r,n=[],a=0;a<t.length;a++){var i=we(t[a],e);if(i instanceof de&&r instanceof de){if("mtext"===i.type&&"mtext"===r.type&&i.getAttribute("mathvariant")===r.getAttribute("mathvariant")){var o;(o=r.children).push.apply(o,i.children);continue}if("mn"===i.type&&"mn"===r.type){var s;(s=r.children).push.apply(s,i.children);continue}if("mi"===i.type&&1===i.children.length&&"mn"===r.type){var h=i.children[0];if(h instanceof pe&&"."===h.text){var l;(l=r.children).push.apply(l,i.children);continue}}}n.push(i),r=i}return n},be=function(t,e){return xe(ye(t,e))},we=function(t,e){if(!t)return new fe.MathNode("mrow");if($t[t.type])return $t[t.type](t,e);throw new o("Got group of unknown type: '"+t.type+"'")};var ke=function(t){return new kt({style:t.displayMode?w.DISPLAY:w.TEXT,maxSize:t.maxSize})},Se=function(t,e){if(e.displayMode){var r=["katex-display"];e.leqno&&r.push("leqno"),e.fleqn&&r.push("fleqn"),t=Dt.makeSpan(r,[t])}return t},ze=function(t,e,r){var n=ke(r),a=function(t,e,r){var n,a=ye(t,r);n=1===a.length&&a[0]instanceof de&&c.contains(["mrow","mtable"],a[0].type)?a[0]:new fe.MathNode("mrow",a);var i=new fe.MathNode("annotation",[new fe.TextNode(e)]);i.setAttribute("encoding","application/x-tex");var o=new fe.MathNode("semantics",[n,i]),s=new fe.MathNode("math",[o]);return Dt.makeSpan(["katex-mathml"],[s])}(t,e,n),i=ce(t,n),o=Dt.makeSpan(["katex"],[a,i]);return Se(o,r)},Me={widehat:"^",widecheck:"\u02c7",widetilde:"~",utilde:"~",overleftarrow:"\u2190",underleftarrow:"\u2190",xleftarrow:"\u2190",overrightarrow:"\u2192",underrightarrow:"\u2192",xrightarrow:"\u2192",underbrace:"\u23b5",overbrace:"\u23de",overleftrightarrow:"\u2194",underleftrightarrow:"\u2194",xleftrightarrow:"\u2194",Overrightarrow:"\u21d2",xRightarrow:"\u21d2",overleftharpoon:"\u21bc",xleftharpoonup:"\u21bc",overrightharpoon:"\u21c0",xrightharpoonup:"\u21c0",xLeftarrow:"\u21d0",xLeftrightarrow:"\u21d4",xhookleftarrow:"\u21a9",xhookrightarrow:"\u21aa",xmapsto:"\u21a6",xrightharpoondown:"\u21c1",xleftharpoondown:"\u21bd",xrightleftharpoons:"\u21cc",xleftrightharpoons:"\u21cb",xtwoheadleftarrow:"\u219e",xtwoheadrightarrow:"\u21a0",xlongequal:"=",xtofrom:"\u21c4",xrightleftarrows:"\u21c4",xrightequilibrium:"\u21cc",xleftequilibrium:"\u21cb"},Te={overrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],overleftarrow:[["leftarrow"],.888,522,"xMinYMin"],underrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],underleftarrow:[["leftarrow"],.888,522,"xMinYMin"],xrightarrow:[["rightarrow"],1.469,522,"xMaxYMin"],xleftarrow:[["leftarrow"],1.469,522,"xMinYMin"],Overrightarrow:[["doublerightarrow"],.888,560,"xMaxYMin"],xRightarrow:[["doublerightarrow"],1.526,560,"xMaxYMin"],xLeftarrow:[["doubleleftarrow"],1.526,560,"xMinYMin"],overleftharpoon:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoonup:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoondown:[["leftharpoondown"],.888,522,"xMinYMin"],overrightharpoon:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoonup:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoondown:[["rightharpoondown"],.888,522,"xMaxYMin"],xlongequal:[["longequal"],.888,334,"xMinYMin"],xtwoheadleftarrow:[["twoheadleftarrow"],.888,334,"xMinYMin"],xtwoheadrightarrow:[["twoheadrightarrow"],.888,334,"xMaxYMin"],overleftrightarrow:[["leftarrow","rightarrow"],.888,522],overbrace:[["leftbrace","midbrace","rightbrace"],1.6,548],underbrace:[["leftbraceunder","midbraceunder","rightbraceunder"],1.6,548],underleftrightarrow:[["leftarrow","rightarrow"],.888,522],xleftrightarrow:[["leftarrow","rightarrow"],1.75,522],xLeftrightarrow:[["doubleleftarrow","doublerightarrow"],1.75,560],xrightleftharpoons:[["leftharpoondownplus","rightharpoonplus"],1.75,716],xleftrightharpoons:[["leftharpoonplus","rightharpoondownplus"],1.75,716],xhookleftarrow:[["leftarrow","righthook"],1.08,522],xhookrightarrow:[["lefthook","rightarrow"],1.08,522],overlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],underlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],overgroup:[["leftgroup","rightgroup"],.888,342],undergroup:[["leftgroupunder","rightgroupunder"],.888,342],xmapsto:[["leftmapsto","rightarrow"],1.5,522],xtofrom:[["leftToFrom","rightToFrom"],1.75,528],xrightleftarrows:[["baraboveleftarrow","rightarrowabovebar"],1.75,901],xrightequilibrium:[["baraboveshortleftharpoon","rightharpoonaboveshortbar"],1.75,716],xleftequilibrium:[["shortbaraboveleftharpoon","shortrightharpoonabovebar"],1.75,716]},Ae=function(t){return"ordgroup"===t.type?t.body.length:1},Be=function(t,e,r,n){var a,i=t.height+t.depth+2*r;if(/fbox|color/.test(e)){if(a=Dt.makeSpan(["stretchy",e],[],n),"fbox"===e){var o=n.color&&n.getColor();o&&(a.style.borderColor=o)}}else{var s=[];/^[bx]cancel$/.test(e)&&s.push(new D({x1:"0",y1:"0",x2:"100%",y2:"100%","stroke-width":"0.046em"})),/^x?cancel$/.test(e)&&s.push(new D({x1:"0",y1:"100%",x2:"100%",y2:"0","stroke-width":"0.046em"}));var h=new R(s,{width:"100%",height:i+"em"});a=Dt.makeSvgSpan([],[h],n)}return a.height=i,a.style.height=i+"em",a},Ce=function(t){var e=new fe.MathNode("mo",[new fe.TextNode(Me[t.substr(1)])]);return e.setAttribute("stretchy","true"),e},Ne=function(t,e){var r=function(){var r=4e5,n=t.label.substr(1);if(c.contains(["widehat","widecheck","widetilde","utilde"],n)){var a,i,o,s=Ae(t.base);if(s>5)"widehat"===n||"widecheck"===n?(a=420,r=2364,o=.42,i=n+"4"):(a=312,r=2340,o=.34,i="tilde4");else{var h=[1,1,2,2,3,3][s];"widehat"===n||"widecheck"===n?(r=[0,1062,2364,2364,2364][h],a=[0,239,300,360,420][h],o=[0,.24,.3,.3,.36,.42][h],i=n+h):(r=[0,600,1033,2339,2340][h],a=[0,260,286,306,312][h],o=[0,.26,.286,.3,.306,.34][h],i="tilde"+h)}var l=new L(i),m=new R([l],{width:"100%",height:o+"em",viewBox:"0 0 "+r+" "+a,preserveAspectRatio:"none"});return{span:Dt.makeSvgSpan([],[m],e),minWidth:0,height:o}}var u,d,p=[],f=Te[n],g=f[0],x=f[1],v=f[2],y=v/1e3,b=g.length;if(1===b)u=["hide-tail"],d=[f[3]];else if(2===b)u=["halfarrow-left","halfarrow-right"],d=["xMinYMin","xMaxYMin"];else{if(3!==b)throw new Error("Correct katexImagesData or update code here to support\n                    "+b+" children.");u=["brace-left","brace-center","brace-right"],d=["xMinYMin","xMidYMin","xMaxYMin"]}for(var w=0;w<b;w++){var k=new L(g[w]),S=new R([k],{width:"400em",height:y+"em",viewBox:"0 0 "+r+" "+v,preserveAspectRatio:d[w]+" slice"}),z=Dt.makeSvgSpan([u[w]],[S],e);if(1===b)return{span:z,minWidth:x,height:y};z.style.height=y+"em",p.push(z)}return{span:Dt.makeSpan(["stretchy"],p,e),minWidth:x,height:y}}(),n=r.span,a=r.minWidth,i=r.height;return n.height=i,n.style.height=i+"em",a>0&&(n.style.minWidth=a+"em"),n},qe=function(t,e){var r,n,a,i=Pt(t,"supsub");i?(r=(n=Ht(i.base,"accent")).base,i.base=r,a=function(t){if(t instanceof q)return t;throw new Error("Expected span<HtmlDomNode> but got "+String(t)+".")}(le(i,e)),i.base=n):r=(n=Ht(t,"accent")).base;var o=le(r,e.havingCrampedStyle()),s=0;if(n.isShifty&&c.isCharacterBox(r)){var h=c.getBaseElem(r);s=function(t){if(t instanceof I)return t;throw new Error("Expected symbolNode but got "+String(t)+".")}(le(h,e.havingCrampedStyle())).skew}var l,m=Math.min(o.height,e.fontMetrics().xHeight);if(n.isStretchy)l=Ne(n,e),l=Dt.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:o},{type:"elem",elem:l,wrapperClasses:["svg-align"],wrapperStyle:s>0?{width:"calc(100% - "+2*s+"em)",marginLeft:2*s+"em"}:void 0}]},e);else{var u,d;"\\vec"===n.label?(u=Dt.staticSvg("vec",e),d=Dt.svgData.vec[1]):((u=Dt.makeSymbol(n.label,"Main-Regular",n.mode,e)).italic=0,d=u.width),l=Dt.makeSpan(["accent-body"],[u]);var p="\\textcircled"===n.label;p&&(l.classes.push("accent-full"),m=o.height);var f=s;p||(f-=d/2),l.style.left=f+"em","\\textcircled"===n.label&&(l.style.top=".2em"),l=Dt.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:o},{type:"kern",size:-m},{type:"elem",elem:l}]},e)}var g=Dt.makeSpan(["mord","accent"],[l],e);return a?(a.children[0]=g,a.height=Math.max(g.height,a.height),a.classes[0]="mord",a):g},Ee=function(t,e){var r=t.isStretchy?Ce(t.label):new fe.MathNode("mo",[ge(t.label,t.mode)]),n=new fe.MathNode("mover",[we(t.base,e),r]);return n.setAttribute("accent","true"),n},Oe=new RegExp(["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring"].map(function(t){return"\\"+t}).join("|"));Zt({type:"accent",names:["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring","\\widecheck","\\widehat","\\widetilde","\\overrightarrow","\\overleftarrow","\\Overrightarrow","\\overleftrightarrow","\\overgroup","\\overlinesegment","\\overleftharpoon","\\overrightharpoon"],props:{numArgs:1},handler:function(t,e){var r=e[0],n=!Oe.test(t.funcName),a=!n||"\\widehat"===t.funcName||"\\widetilde"===t.funcName||"\\widecheck"===t.funcName;return{type:"accent",mode:t.parser.mode,label:t.funcName,isStretchy:n,isShifty:a,base:r}},htmlBuilder:qe,mathmlBuilder:Ee}),Zt({type:"accent",names:["\\'","\\`","\\^","\\~","\\=","\\u","\\.",'\\"',"\\r","\\H","\\v","\\textcircled"],props:{numArgs:1,allowedInText:!0,allowedInMath:!1},handler:function(t,e){var r=e[0];return{type:"accent",mode:t.parser.mode,label:t.funcName,isStretchy:!1,isShifty:!0,base:r}},htmlBuilder:qe,mathmlBuilder:Ee}),Zt({type:"accentUnder",names:["\\underleftarrow","\\underrightarrow","\\underleftrightarrow","\\undergroup","\\underlinesegment","\\utilde"],props:{numArgs:1},handler:function(t,e){var r=t.parser,n=t.funcName,a=e[0];return{type:"accentUnder",mode:r.mode,label:n,base:a}},htmlBuilder:function(t,e){var r=le(t.base,e),n=Ne(t,e),a="\\utilde"===t.label?.12:0,i=Dt.makeVList({positionType:"bottom",positionData:n.height+a,children:[{type:"elem",elem:n,wrapperClasses:["svg-align"]},{type:"kern",size:a},{type:"elem",elem:r}]},e);return Dt.makeSpan(["mord","accentunder"],[i],e)},mathmlBuilder:function(t,e){var r=Ce(t.label),n=new fe.MathNode("munder",[we(t.base,e),r]);return n.setAttribute("accentunder","true"),n}}),Zt({type:"xArrow",names:["\\xleftarrow","\\xrightarrow","\\xLeftarrow","\\xRightarrow","\\xleftrightarrow","\\xLeftrightarrow","\\xhookleftarrow","\\xhookrightarrow","\\xmapsto","\\xrightharpoondown","\\xrightharpoonup","\\xleftharpoondown","\\xleftharpoonup","\\xrightleftharpoons","\\xleftrightharpoons","\\xlongequal","\\xtwoheadrightarrow","\\xtwoheadleftarrow","\\xtofrom","\\xrightleftarrows","\\xrightequilibrium","\\xleftequilibrium"],props:{numArgs:1,numOptionalArgs:1},handler:function(t,e,r){var n=t.parser,a=t.funcName;return{type:"xArrow",mode:n.mode,label:a,body:e[0],below:r[0]}},htmlBuilder:function(t,e){var r,n=e.style,a=e.havingStyle(n.sup()),i=Dt.wrapFragment(le(t.body,a,e),e);i.classes.push("x-arrow-pad"),t.below&&(a=e.havingStyle(n.sub()),(r=Dt.wrapFragment(le(t.below,a,e),e)).classes.push("x-arrow-pad"));var o,s=Ne(t,e),h=-e.fontMetrics().axisHeight+.5*s.height,l=-e.fontMetrics().axisHeight-.5*s.height-.111;if((i.depth>.25||"\\xleftequilibrium"===t.label)&&(l-=i.depth),r){var m=-e.fontMetrics().axisHeight+r.height+.5*s.height+.111;o=Dt.makeVList({positionType:"individualShift",children:[{type:"elem",elem:i,shift:l},{type:"elem",elem:s,shift:h},{type:"elem",elem:r,shift:m}]},e)}else o=Dt.makeVList({positionType:"individualShift",children:[{type:"elem",elem:i,shift:l},{type:"elem",elem:s,shift:h}]},e);return o.children[0].children[0].children[1].classes.push("svg-align"),Dt.makeSpan(["mrel","x-arrow"],[o],e)},mathmlBuilder:function(t,e){var r,n,a=Ce(t.label);if(t.body){var i=we(t.body,e);t.below?(n=we(t.below,e),r=new fe.MathNode("munderover",[a,n,i])):r=new fe.MathNode("mover",[a,i])}else t.below?(n=we(t.below,e),r=new fe.MathNode("munder",[a,n])):r=new fe.MathNode("mover",[a]);return r}}),Zt({type:"textord",names:["\\@char"],props:{numArgs:1,allowedInText:!0},handler:function(t,e){for(var r=t.parser,n=Ht(e[0],"ordgroup").body,a="",i=0;i<n.length;i++){a+=Ht(n[i],"textord").text}var s=parseInt(a);if(isNaN(s))throw new o("\\@char has non-numeric argument "+a);return{type:"textord",mode:r.mode,text:String.fromCharCode(s)}}});var Ie=function(t,e){var r=ae(t.body,e.withColor(t.color),!1);return Dt.makeFragment(r)},Re=function(t,e){var r=ye(t.body,e),n=new fe.MathNode("mstyle",r);return n.setAttribute("mathcolor",t.color),n};Zt({type:"color",names:["\\textcolor"],props:{numArgs:2,allowedInText:!0,greediness:3,argTypes:["color","original"]},handler:function(t,e){var r=t.parser,n=Ht(e[0],"color-token").color,a=e[1];return{type:"color",mode:r.mode,color:n,body:Jt(a)}},htmlBuilder:Ie,mathmlBuilder:Re}),Zt({type:"color",names:["\\blue","\\orange","\\pink","\\red","\\green","\\gray","\\purple","\\blueA","\\blueB","\\blueC","\\blueD","\\blueE","\\tealA","\\tealB","\\tealC","\\tealD","\\tealE","\\greenA","\\greenB","\\greenC","\\greenD","\\greenE","\\goldA","\\goldB","\\goldC","\\goldD","\\goldE","\\redA","\\redB","\\redC","\\redD","\\redE","\\maroonA","\\maroonB","\\maroonC","\\maroonD","\\maroonE","\\purpleA","\\purpleB","\\purpleC","\\purpleD","\\purpleE","\\mintA","\\mintB","\\mintC","\\grayA","\\grayB","\\grayC","\\grayD","\\grayE","\\grayF","\\grayG","\\grayH","\\grayI","\\kaBlue","\\kaGreen"],props:{numArgs:1,allowedInText:!0,greediness:3},handler:function(t,e){var r=t.parser,n=t.funcName,a=e[0];return{type:"color",mode:r.mode,color:"katex-"+n.slice(1),body:Jt(a)}},htmlBuilder:Ie,mathmlBuilder:Re}),Zt({type:"color",names:["\\color"],props:{numArgs:1,allowedInText:!0,greediness:3,argTypes:["color"]},handler:function(t,e){var r=t.parser,n=t.breakOnTokenText,a=Ht(e[0],"color-token").color,i=r.parseExpression(!0,n);return{type:"color",mode:r.mode,color:a,body:i}},htmlBuilder:Ie,mathmlBuilder:Re}),Zt({type:"cr",names:["\\cr","\\newline"],props:{numArgs:0,numOptionalArgs:1,argTypes:["size"],allowedInText:!0},handler:function(t,e,r){var n=t.parser,a=t.funcName,i=r[0],o="\\cr"===a,s=!1;return o||(s=!n.settings.displayMode||!n.settings.useStrictBehavior("newLineInDisplayMode","In LaTeX, \\\\ or \\newline does nothing in display mode")),{type:"cr",mode:n.mode,newLine:s,newRow:o,size:i&&Ht(i,"size").value}},htmlBuilder:function(t,e){if(t.newRow)throw new o("\\cr valid only within a tabular/array environment");var r=Dt.makeSpan(["mspace"],[],e);return t.newLine&&(r.classes.push("newline"),t.size&&(r.style.marginTop=Mt(t.size,e)+"em")),r},mathmlBuilder:function(t,e){var r=new fe.MathNode("mspace");return t.newLine&&(r.setAttribute("linebreak","newline"),t.size&&r.setAttribute("height",Mt(t.size,e)+"em")),r}});var Le=function(t,e,r){var n=V(_.math[t]&&_.math[t].replace||t,e,r);if(!n)throw new Error("Unsupported symbol "+t+" and font size "+e+".");return n},De=function(t,e,r,n){var a=r.havingBaseStyle(e),i=Dt.makeSpan(n.concat(a.sizingClasses(r)),[t],r),o=a.sizeMultiplier/r.sizeMultiplier;return i.height*=o,i.depth*=o,i.maxFontSize=a.sizeMultiplier,i},He=function(t,e,r){var n=e.havingBaseStyle(r),a=(1-e.sizeMultiplier/n.sizeMultiplier)*e.fontMetrics().axisHeight;t.classes.push("delimcenter"),t.style.top=a+"em",t.height-=a,t.depth+=a},Pe=function(t,e,r,n,a,i){var o=function(t,e,r,n){return Dt.makeSymbol(t,"Size"+e+"-Regular",r,n)}(t,e,a,n),s=De(Dt.makeSpan(["delimsizing","size"+e],[o],n),w.TEXT,n,i);return r&&He(s,n,w.TEXT),s},Fe=function(t,e,r){var n;return n="Size1-Regular"===e?"delim-size1":"delim-size4",{type:"elem",elem:Dt.makeSpan(["delimsizinginner",n],[Dt.makeSpan([],[Dt.makeSymbol(t,e,r)])])}},Ve=function(t,e,r,n,a,i){var o,s,h,l;o=h=l=t,s=null;var m="Size1-Regular";"\\uparrow"===t?h=l="\u23d0":"\\Uparrow"===t?h=l="\u2016":"\\downarrow"===t?o=h="\u23d0":"\\Downarrow"===t?o=h="\u2016":"\\updownarrow"===t?(o="\\uparrow",h="\u23d0",l="\\downarrow"):"\\Updownarrow"===t?(o="\\Uparrow",h="\u2016",l="\\Downarrow"):"["===t||"\\lbrack"===t?(o="\u23a1",h="\u23a2",l="\u23a3",m="Size4-Regular"):"]"===t||"\\rbrack"===t?(o="\u23a4",h="\u23a5",l="\u23a6",m="Size4-Regular"):"\\lfloor"===t||"\u230a"===t?(h=o="\u23a2",l="\u23a3",m="Size4-Regular"):"\\lceil"===t||"\u2308"===t?(o="\u23a1",h=l="\u23a2",m="Size4-Regular"):"\\rfloor"===t||"\u230b"===t?(h=o="\u23a5",l="\u23a6",m="Size4-Regular"):"\\rceil"===t||"\u2309"===t?(o="\u23a4",h=l="\u23a5",m="Size4-Regular"):"("===t||"\\lparen"===t?(o="\u239b",h="\u239c",l="\u239d",m="Size4-Regular"):")"===t||"\\rparen"===t?(o="\u239e",h="\u239f",l="\u23a0",m="Size4-Regular"):"\\{"===t||"\\lbrace"===t?(o="\u23a7",s="\u23a8",l="\u23a9",h="\u23aa",m="Size4-Regular"):"\\}"===t||"\\rbrace"===t?(o="\u23ab",s="\u23ac",l="\u23ad",h="\u23aa",m="Size4-Regular"):"\\lgroup"===t||"\u27ee"===t?(o="\u23a7",l="\u23a9",h="\u23aa",m="Size4-Regular"):"\\rgroup"===t||"\u27ef"===t?(o="\u23ab",l="\u23ad",h="\u23aa",m="Size4-Regular"):"\\lmoustache"===t||"\u23b0"===t?(o="\u23a7",l="\u23ad",h="\u23aa",m="Size4-Regular"):"\\rmoustache"!==t&&"\u23b1"!==t||(o="\u23ab",l="\u23a9",h="\u23aa",m="Size4-Regular");var c=Le(o,m,a),u=c.height+c.depth,d=Le(h,m,a),p=d.height+d.depth,f=Le(l,m,a),g=f.height+f.depth,x=0,v=1;if(null!==s){var y=Le(s,m,a);x=y.height+y.depth,v=2}var b=u+g+x,k=Math.ceil((e-b)/(v*p)),S=b+k*v*p,z=n.fontMetrics().axisHeight;r&&(z*=n.sizeMultiplier);var M=S/2-z,T=[];if(T.push(Fe(l,m,a)),null===s)for(var A=0;A<k;A++)T.push(Fe(h,m,a));else{for(var B=0;B<k;B++)T.push(Fe(h,m,a));T.push(Fe(s,m,a));for(var C=0;C<k;C++)T.push(Fe(h,m,a))}T.push(Fe(o,m,a));var N=n.havingBaseStyle(w.TEXT),q=Dt.makeVList({positionType:"bottom",positionData:M,children:T},N);return De(Dt.makeSpan(["delimsizing","mult"],[q],N),w.TEXT,n,i)},Ue=function(t,e,r,n){var a;"sqrtTall"===t&&(a="M702 80H400000v40H742v"+(r-54-80)+"l-4 4-4 4c-.667.7\n-2 1.5-4 2.5s-4.167 1.833-6.5 2.5-5.5 1-9.5 1h-12l-28-84c-16.667-52-96.667\n-294.333-240-727l-212 -643 -85 170c-4-3.333-8.333-7.667-13 -13l-13-13l77-155\n 77-156c66 199.333 139 419.667 219 661 l218 661zM702 80H400000v40H742z");var i=new L(t,a),o=new R([i],{width:"400em",height:e+"em",viewBox:"0 0 400000 "+r,preserveAspectRatio:"xMinYMin slice"});return Dt.makeSvgSpan(["hide-tail"],[o],n)},Ge=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","\u230a","\u230b","\\lceil","\\rceil","\u2308","\u2309","\\surd"],Xe=["\\uparrow","\\downarrow","\\updownarrow","\\Uparrow","\\Downarrow","\\Updownarrow","|","\\|","\\vert","\\Vert","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","\u27ee","\u27ef","\\lmoustache","\\rmoustache","\u23b0","\u23b1"],Ye=["<",">","\\langle","\\rangle","https://coderthemes.com/","\\backslash","\\lt","\\gt"],_e=[0,1.2,1.8,2.4,3],We=[{type:"small",style:w.SCRIPTSCRIPT},{type:"small",style:w.SCRIPT},{type:"small",style:w.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4}],je=[{type:"small",style:w.SCRIPTSCRIPT},{type:"small",style:w.SCRIPT},{type:"small",style:w.TEXT},{type:"stack"}],$e=[{type:"small",style:w.SCRIPTSCRIPT},{type:"small",style:w.SCRIPT},{type:"small",style:w.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4},{type:"stack"}],Ze=function(t){if("small"===t.type)return"Main-Regular";if("large"===t.type)return"Size"+t.size+"-Regular";if("stack"===t.type)return"Size4-Regular";throw new Error("Add support for delim type '"+t.type+"' here.")},Ke=function(t,e,r,n){for(var a=Math.min(2,3-n.style.size);a<r.length&&"stack"!==r[a].type;a++){var i=Le(t,Ze(r[a]),"math"),o=i.height+i.depth;if("small"===r[a].type&&(o*=n.havingBaseStyle(r[a].style).sizeMultiplier),o>e)return r[a]}return r[r.length-1]},Je=function(t,e,r,n,a,i){var o;"<"===t||"\\lt"===t||"\u27e8"===t?t="\\langle":">"!==t&&"\\gt"!==t&&"\u27e9"!==t||(t="\\rangle"),o=c.contains(Ye,t)?We:c.contains(Ge,t)?$e:je;var s=Ke(t,e,o,n);return"small"===s.type?function(t,e,r,n,a,i){var o=Dt.makeSymbol(t,"Main-Regular",a,n),s=De(o,e,n,i);return r&&He(s,n,e),s}(t,s.style,r,n,a,i):"large"===s.type?Pe(t,s.size,r,n,a,i):Ve(t,e,r,n,a,i)},Qe=function(t,e){var r,n,a=e.havingBaseSizing(),i=Ke("\\surd",t*a.sizeMultiplier,$e,a),o=a.sizeMultiplier,s=0,h=0,l=0;return"small"===i.type?(t<1?o=1:t<1.4&&(o=.7),h=1/o,(r=Ue("sqrtMain",s=1.08/o,l=1080,e)).style.minWidth="0.853em",n=.833/o):"large"===i.type?(l=1080*_e[i.size],h=_e[i.size]/o,s=(_e[i.size]+.08)/o,(r=Ue("sqrtSize"+i.size,s,l,e)).style.minWidth="1.02em",n=1/o):(s=t+.08,h=t,l=Math.floor(1e3*t)+80,(r=Ue("sqrtTall",s,l,e)).style.minWidth="0.742em",n=1.056),r.height=h,r.style.height=s+"em",{span:r,advanceWidth:n,ruleWidth:e.fontMetrics().sqrtRuleThickness*o}},tr=function(t,e,r,n,a){if("<"===t||"\\lt"===t||"\u27e8"===t?t="\\langle":">"!==t&&"\\gt"!==t&&"\u27e9"!==t||(t="\\rangle"),c.contains(Ge,t)||c.contains(Ye,t))return Pe(t,e,!1,r,n,a);if(c.contains(Xe,t))return Ve(t,_e[e],!1,r,n,a);throw new o("Illegal delimiter: '"+t+"'")},er=Je,rr=function(t,e,r,n,a,i){var o=n.fontMetrics().axisHeight*n.sizeMultiplier,s=5/n.fontMetrics().ptPerEm,h=Math.max(e-o,r+o),l=Math.max(h/500*901,2*h-s);return Je(t,l,!0,n,a,i)},nr={"\\bigl":{mclass:"mopen",size:1},"\\Bigl":{mclass:"mopen",size:2},"\\biggl":{mclass:"mopen",size:3},"\\Biggl":{mclass:"mopen",size:4},"\\bigr":{mclass:"mclose",size:1},"\\Bigr":{mclass:"mclose",size:2},"\\biggr":{mclass:"mclose",size:3},"\\Biggr":{mclass:"mclose",size:4},"\\bigm":{mclass:"mrel",size:1},"\\Bigm":{mclass:"mrel",size:2},"\\biggm":{mclass:"mrel",size:3},"\\Biggm":{mclass:"mrel",size:4},"\\big":{mclass:"mord",size:1},"\\Big":{mclass:"mord",size:2},"\\bigg":{mclass:"mord",size:3},"\\Bigg":{mclass:"mord",size:4}},ar=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","\u230a","\u230b","\\lceil","\\rceil","\u2308","\u2309","<",">","\\langle","\u27e8","\\rangle","\u27e9","\\lt","\\gt","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","\u27ee","\u27ef","\\lmoustache","\\rmoustache","\u23b0","\u23b1","https://coderthemes.com/","\\backslash","|","\\vert","\\|","\\Vert","\\uparrow","\\Uparrow","\\downarrow","\\Downarrow","\\updownarrow","\\Updownarrow","."];function ir(t,e){var r=Vt(t);if(r&&c.contains(ar,r.text))return r;throw new o("Invalid delimiter: '"+(r?r.text:JSON.stringify(t))+"' after '"+e.funcName+"'",t)}function or(t){if(!t.body)throw new Error("Bug: The leftright ParseNode wasn't fully parsed.")}Zt({type:"delimsizing",names:["\\bigl","\\Bigl","\\biggl","\\Biggl","\\bigr","\\Bigr","\\biggr","\\Biggr","\\bigm","\\Bigm","\\biggm","\\Biggm","\\big","\\Big","\\bigg","\\Bigg"],props:{numArgs:1},handler:function(t,e){var r=ir(e[0],t);return{type:"delimsizing",mode:t.parser.mode,size:nr[t.funcName].size,mclass:nr[t.funcName].mclass,delim:r.text}},htmlBuilder:function(t,e){return"."===t.delim?Dt.makeSpan([t.mclass]):tr(t.delim,t.size,e,t.mode,[t.mclass])},mathmlBuilder:function(t){var e=[];"."!==t.delim&&e.push(ge(t.delim,t.mode));var r=new fe.MathNode("mo",e);return"mopen"===t.mclass||"mclose"===t.mclass?r.setAttribute("fence","true"):r.setAttribute("fence","false"),r}}),Zt({type:"leftright-right",names:["\\right"],props:{numArgs:1},handler:function(t,e){return{type:"leftright-right",mode:t.parser.mode,delim:ir(e[0],t).text}}}),Zt({type:"leftright",names:["\\left"],props:{numArgs:1},handler:function(t,e){var r=ir(e[0],t),n=t.parser;++n.leftrightDepth;var a=n.parseExpression(!1);--n.leftrightDepth,n.expect("\\right",!1);var i=Ht(n.parseFunction(),"leftright-right");return{type:"leftright",mode:n.mode,body:a,left:r.text,right:i.delim}},htmlBuilder:function(t,e){or(t);for(var r,n,a=ae(t.body,e,!0,["mopen","mclose"]),i=0,o=0,s=!1,h=0;h<a.length;h++)a[h].isMiddle?s=!0:(i=Math.max(a[h].height,i),o=Math.max(a[h].depth,o));if(i*=e.sizeMultiplier,o*=e.sizeMultiplier,r="."===t.left?he(e,["mopen"]):rr(t.left,i,o,e,t.mode,["mopen"]),a.unshift(r),s)for(var l=1;l<a.length;l++){var m=a[l].isMiddle;m&&(a[l]=rr(m.delim,i,o,m.options,t.mode,[]))}return n="."===t.right?he(e,["mclose"]):rr(t.right,i,o,e,t.mode,["mclose"]),a.push(n),Dt.makeSpan(["minner"],a,e)},mathmlBuilder:function(t,e){or(t);var r=ye(t.body,e);if("."!==t.left){var n=new fe.MathNode("mo",[ge(t.left,t.mode)]);n.setAttribute("fence","true"),r.unshift(n)}if("."!==t.right){var a=new fe.MathNode("mo",[ge(t.right,t.mode)]);a.setAttribute("fence","true"),r.push(a)}return xe(r)}}),Zt({type:"middle",names:["\\middle"],props:{numArgs:1},handler:function(t,e){var r=ir(e[0],t);if(!t.parser.leftrightDepth)throw new o("\\middle without preceding \\left",r);return{type:"middle",mode:t.parser.mode,delim:r.text}},htmlBuilder:function(t,e){var r;if("."===t.delim)r=he(e,[]);else{r=tr(t.delim,1,e,t.mode,[]);var n={delim:t.delim,options:e};r.isMiddle=n}return r},mathmlBuilder:function(t,e){var r=new fe.MathNode("mo",[ge(t.delim,t.mode)]);return r.setAttribute("fence","true"),r}});var sr=function(t,e){var r,n,a=Dt.wrapFragment(le(t.body,e),e),i=t.label.substr(1),o=e.sizeMultiplier,s=0,h=c.isCharacterBox(t.body);if("sout"===i)(r=Dt.makeSpan(["stretchy","sout"])).height=e.fontMetrics().defaultRuleThickness/o,s=-.5*e.fontMetrics().xHeight;else{/cancel/.test(i)?h||a.classes.push("cancel-pad"):a.classes.push("boxpad");var l=0;l=/box/.test(i)?"colorbox"===i?.3:.34:h?.2:0,r=Be(a,i,l,e),s=a.depth+l,t.backgroundColor&&(r.style.backgroundColor=t.backgroundColor,t.borderColor&&(r.style.borderColor=t.borderColor))}return n=t.backgroundColor?Dt.makeVList({positionType:"individualShift",children:[{type:"elem",elem:r,shift:s},{type:"elem",elem:a,shift:0}]},e):Dt.makeVList({positionType:"individualShift",children:[{type:"elem",elem:a,shift:0},{type:"elem",elem:r,shift:s,wrapperClasses:/cancel/.test(i)?["svg-align"]:[]}]},e),/cancel/.test(i)&&(n.height=a.height,n.depth=a.depth),/cancel/.test(i)&&!h?Dt.makeSpan(["mord","cancel-lap"],[n],e):Dt.makeSpan(["mord"],[n],e)},hr=function(t,e){var r=new fe.MathNode("menclose",[we(t.body,e)]);switch(t.label){case"\\cancel":r.setAttribute("notation","updiagonalstrike");break;case"\\bcancel":r.setAttribute("notation","downdiagonalstrike");break;case"\\sout":r.setAttribute("notation","horizontalstrike");break;case"\\fbox":case"\\fcolorbox":r.setAttribute("notation","box");break;case"\\xcancel":r.setAttribute("notation","updiagonalstrike downdiagonalstrike")}return t.backgroundColor&&r.setAttribute("mathbackground",t.backgroundColor),r};Zt({type:"enclose",names:["\\colorbox"],props:{numArgs:2,allowedInText:!0,greediness:3,argTypes:["color","text"]},handler:function(t,e,r){var n=t.parser,a=t.funcName,i=Ht(e[0],"color-token").color,o=e[1];return{type:"enclose",mode:n.mode,label:a,backgroundColor:i,body:o}},htmlBuilder:sr,mathmlBuilder:hr}),Zt({type:"enclose",names:["\\fcolorbox"],props:{numArgs:3,allowedInText:!0,greediness:3,argTypes:["color","color","text"]},handler:function(t,e,r){var n=t.parser,a=t.funcName,i=Ht(e[0],"color-token").color,o=Ht(e[1],"color-token").color,s=e[2];return{type:"enclose",mode:n.mode,label:a,backgroundColor:o,borderColor:i,body:s}},htmlBuilder:sr,mathmlBuilder:hr}),Zt({type:"enclose",names:["\\fbox"],props:{numArgs:1,argTypes:["text"],allowedInText:!0},handler:function(t,e){return{type:"enclose",mode:t.parser.mode,label:"\\fbox",body:e[0]}}}),Zt({type:"enclose",names:["\\cancel","\\bcancel","\\xcancel","\\sout"],props:{numArgs:1},handler:function(t,e,r){var n=t.parser,a=t.funcName,i=e[0];return{type:"enclose",mode:n.mode,label:a,body:i}},htmlBuilder:sr,mathmlBuilder:hr});var lr={};function mr(t){for(var e=t.type,r=t.names,n=t.props,a=t.handler,i=t.htmlBuilder,o=t.mathmlBuilder,s={type:e,numArgs:n.numArgs||0,greediness:1,allowedInText:!1,numOptionalArgs:0,handler:a},h=0;h<r.length;++h)lr[r[h]]=s;i&&(jt[e]=i),o&&($t[e]=o)}function cr(t){var e=[];t.consumeSpaces();for(var r=t.nextToken.text;"\\hline"===r||"\\hdashline"===r;)t.consume(),e.push("\\hdashline"===r),t.consumeSpaces(),r=t.nextToken.text;return e}function ur(t,e,r){var n=e.hskipBeforeAndAfter,a=e.addJot,i=e.cols,s=e.arraystretch;if(t.gullet.beginGroup(),t.gullet.macros.set("\\\\","\\cr"),!s){var h=t.gullet.expandMacroAsText("\\arraystretch");if(null==h)s=1;else if(!(s=parseFloat(h))||s<0)throw new o("Invalid \\arraystretch: "+h)}var l=[],m=[l],c=[],u=[];for(u.push(cr(t));;){var d=t.parseExpression(!1,"\\cr");d={type:"ordgroup",mode:t.mode,body:d},r&&(d={type:"styling",mode:t.mode,style:r,body:[d]}),l.push(d);var p=t.nextToken.text;if("&"===p)t.consume();else{if("\\end"===p){1===l.length&&"styling"===d.type&&0===d.body[0].body.length&&m.pop(),u.length<m.length+1&&u.push([]);break}if("\\cr"!==p)throw new o("Expected & or \\\\ or \\cr or \\end",t.nextToken);var f=Ht(t.parseFunction(),"cr");c.push(f.size),u.push(cr(t)),l=[],m.push(l)}}return t.gullet.endGroup(),{type:"array",mode:t.mode,addJot:a,arraystretch:s,body:m,cols:i,rowGaps:c,hskipBeforeAndAfter:n,hLinesBeforeRow:u}}function dr(t){return"d"===t.substr(0,1)?"display":"text"}var pr=function(t,e){var r,n,a=t.body.length,i=t.hLinesBeforeRow,s=0,h=new Array(a),l=[],m=1/e.fontMetrics().ptPerEm,u=5*m,d=12*m,p=3*m,f=t.arraystretch*d,g=.7*f,x=.3*f,v=0;function y(t){for(var e=0;e<t.length;++e)e>0&&(v+=.25),l.push({pos:v,isDashed:t[e]})}for(y(i[0]),r=0;r<t.body.length;++r){var b=t.body[r],w=g,k=x;s<b.length&&(s=b.length);var S=new Array(b.length);for(n=0;n<b.length;++n){var z=le(b[n],e);k<z.depth&&(k=z.depth),w<z.height&&(w=z.height),S[n]=z}var M=t.rowGaps[r],T=0;M&&(T=Mt(M,e))>0&&(k<(T+=x)&&(k=T),T=0),t.addJot&&(k+=p),S.height=w,S.depth=k,v+=w,S.pos=v,v+=k+T,h[r]=S,y(i[r+1])}var A,B,C=v/2+e.fontMetrics().axisHeight,N=t.cols||[],q=[];for(n=0,B=0;n<s||B<N.length;++n,++B){for(var E=N[B]||{},O=!0;"separator"===E.type;){if(O||((A=Dt.makeSpan(["arraycolsep"],[])).style.width=e.fontMetrics().doubleRuleSep+"em",q.push(A)),"|"===E.separator){var I=Dt.makeSpan(["vertical-separator"],[],e);I.style.height=v+"em",I.style.verticalAlign=-(v-C)+"em",q.push(I)}else{if(":"!==E.separator)throw new o("Invalid separator type: "+E.separator);var R=Dt.makeSpan(["vertical-separator","vs-dashed"],[],e);R.style.height=v+"em",R.style.verticalAlign=-(v-C)+"em",q.push(R)}E=N[++B]||{},O=!1}if(!(n>=s)){var L=void 0;(n>0||t.hskipBeforeAndAfter)&&0!==(L=c.deflt(E.pregap,u))&&((A=Dt.makeSpan(["arraycolsep"],[])).style.width=L+"em",q.push(A));var D=[];for(r=0;r<a;++r){var H=h[r],P=H[n];if(P){var F=H.pos-C;P.depth=H.depth,P.height=H.height,D.push({type:"elem",elem:P,shift:F})}}D=Dt.makeVList({positionType:"individualShift",children:D},e),D=Dt.makeSpan(["col-align-"+(E.align||"c")],[D]),q.push(D),(n<s-1||t.hskipBeforeAndAfter)&&0!==(L=c.deflt(E.postgap,u))&&((A=Dt.makeSpan(["arraycolsep"],[])).style.width=L+"em",q.push(A))}}if(h=Dt.makeSpan(["mtable"],q),l.length>0){for(var V=Dt.makeLineSpan("hline",e,.05),U=Dt.makeLineSpan("hdashline",e,.05),G=[{type:"elem",elem:h,shift:0}];l.length>0;){var X=l.pop(),Y=X.pos-C;X.isDashed?G.push({type:"elem",elem:U,shift:Y}):G.push({type:"elem",elem:V,shift:Y})}h=Dt.makeVList({positionType:"individualShift",children:G},e)}return Dt.makeSpan(["mord"],[h],e)},fr=function(t,e){return new fe.MathNode("mtable",t.body.map(function(t){return new fe.MathNode("mtr",t.map(function(t){return new fe.MathNode("mtd",[we(t,e)])}))}))},gr=function(t,e){var r,n=[],a=ur(t.parser,{cols:n,addJot:!0},"display"),i=0,s={type:"ordgroup",mode:t.mode,body:[]},h=Pt(e[0],"ordgroup");if(h){for(var l="",m=0;m<h.body.length;m++){l+=Ht(h.body[m],"textord").text}r=Number(l),i=2*r}var c=!i;a.body.forEach(function(t){for(var e=1;e<t.length;e+=2){var n=Ht(t[e],"styling");Ht(n.body[0],"ordgroup").body.unshift(s)}if(c)i<t.length&&(i=t.length);else{var a=t.length/2;if(r<a)throw new o("Too many math in a row: expected "+r+", but got "+a,t[0])}});for(var u=0;u<i;++u){var d="r",p=0;u%2==1?d="l":u>0&&c&&(p=1),n[u]={type:"align",align:d,pregap:p,postgap:0}}return a};mr({type:"array",names:["array","darray"],props:{numArgs:1},handler:function(t,e){var r={cols:(Vt(e[0])?[e[0]]:Ht(e[0],"ordgroup").body).map(function(t){var e=function(t){var e=Vt(t);if(!e)throw new Error("Expected node of symbol group type, but got "+(t?"node of type "+t.type:String(t)));return e}(t).text;if(-1!=="lcr".indexOf(e))return{type:"align",align:e};if("|"===e)return{type:"separator",separator:"|"};if(":"===e)return{type:"separator",separator:":"};throw new o("Unknown column alignment: "+e,t)}),hskipBeforeAndAfter:!0};return ur(t.parser,r,dr(t.envName))},htmlBuilder:pr,mathmlBuilder:fr}),mr({type:"array",names:["matrix","pmatrix","bmatrix","Bmatrix","vmatrix","Vmatrix"],props:{numArgs:0},handler:function(t){var e={matrix:null,pmatrix:["(",")"],bmatrix:["[","]"],Bmatrix:["\\{","\\}"],vmatrix:["|","|"],Vmatrix:["\\Vert","\\Vert"]}[t.envName],r=ur(t.parser,{hskipBeforeAndAfter:!1},dr(t.envName));return e?{type:"leftright",mode:t.mode,body:[r],left:e[0],right:e[1]}:r},htmlBuilder:pr,mathmlBuilder:fr}),mr({type:"array",names:["cases","dcases"],props:{numArgs:0},handler:function(t){var e=ur(t.parser,{arraystretch:1.2,cols:[{type:"align",align:"l",pregap:0,postgap:1},{type:"align",align:"l",pregap:0,postgap:0}]},dr(t.envName));return{type:"leftright",mode:t.mode,body:[e],left:"\\{",right:"."}},htmlBuilder:pr,mathmlBuilder:fr}),mr({type:"array",names:["aligned"],props:{numArgs:0},handler:gr,htmlBuilder:pr,mathmlBuilder:fr}),mr({type:"array",names:["gathered"],props:{numArgs:0},handler:function(t){return ur(t.parser,{cols:[{type:"align",align:"c"}],addJot:!0},"display")},htmlBuilder:pr,mathmlBuilder:fr}),mr({type:"array",names:["alignedat"],props:{numArgs:1},handler:gr,htmlBuilder:pr,mathmlBuilder:fr}),Zt({type:"text",names:["\\hline","\\hdashline"],props:{numArgs:0,allowedInText:!0,allowedInMath:!0},handler:function(t,e){throw new o(t.funcName+" valid only within array environment")}});var xr=lr;Zt({type:"environment",names:["\\begin","\\end"],props:{numArgs:1,argTypes:["text"]},handler:function(t,e){var r=t.parser,n=t.funcName,a=e[0];if("ordgroup"!==a.type)throw new o("Invalid environment name",a);for(var i="",s=0;s<a.body.length;++s)i+=Ht(a.body[s],"textord").text;if("\\begin"===n){if(!xr.hasOwnProperty(i))throw new o("No such environment: "+i,a);var h=xr[i],l=r.parseArguments("\\begin{"+i+"}",h),m=l.args,c=l.optArgs,u={mode:r.mode,envName:i,parser:r},d=h.handler(u,m,c);r.expect("\\end",!1);var p=r.nextToken,f=Ht(r.parseFunction(),"environment");if(f.name!==i)throw new o("Mismatch: \\begin{"+i+"} matched by \\end{"+f.name+"}",p);return d}return{type:"environment",mode:r.mode,name:i,nameGroup:a}}});var vr=Dt.makeSpan;function yr(t,e){var r=ae(t.body,e,!0);return vr([t.mclass],r,e)}function br(t,e){var r=ye(t.body,e);return fe.newDocumentFragment(r)}Zt({type:"mclass",names:["\\mathord","\\mathbin","\\mathrel","\\mathopen","\\mathclose","\\mathpunct","\\mathinner"],props:{numArgs:1},handler:function(t,e){var r=t.parser,n=t.funcName,a=e[0];return{type:"mclass",mode:r.mode,mclass:"m"+n.substr(5),body:Jt(a)}},htmlBuilder:yr,mathmlBuilder:br});var wr=function(t){var e="ordgroup"===t.type&&t.body.length?t.body[0]:t;return"atom"!==e.type||"bin"!==e.family&&"rel"!==e.family?"mord":"m"+e.family};Zt({type:"mclass",names:["\\@binrel"],props:{numArgs:2},handler:function(t,e){return{type:"mclass",mode:t.parser.mode,mclass:wr(e[0]),body:[e[1]]}}}),Zt({type:"mclass",names:["\\stackrel","\\overset","\\underset"],props:{numArgs:2},handler:function(t,e){var r,n=t.parser,a=t.funcName,i=e[1],o=e[0];r="\\stackrel"!==a?wr(i):"mrel";var s={type:"op",mode:i.mode,limits:!0,alwaysHandleSupSub:!0,symbol:!1,suppressBaseShift:"\\stackrel"!==a,body:Jt(i)},h={type:"supsub",mode:o.mode,base:s,sup:"\\underset"===a?null:o,sub:"\\underset"===a?o:null};return{type:"mclass",mode:n.mode,mclass:r,body:[h]}},htmlBuilder:yr,mathmlBuilder:br});var kr=function(t,e){var r=t.font,n=e.withFont(r);return le(t.body,n)},Sr=function(t,e){var r=t.font,n=e.withFont(r);return we(t.body,n)},zr={"\\Bbb":"\\mathbb","\\bold":"\\mathbf","\\frak":"\\mathfrak","\\bm":"\\boldsymbol"};Zt({type:"font",names:["\\mathrm","\\mathit","\\mathbf","\\mathnormal","\\mathbb","\\mathcal","\\mathfrak","\\mathscr","\\mathsf","\\mathtt","\\Bbb","\\bold","\\frak"],props:{numArgs:1,greediness:2},handler:function(t,e){var r=t.parser,n=t.funcName,a=e[0],i=n;return i in zr&&(i=zr[i]),{type:"font",mode:r.mode,font:i.slice(1),body:a}},htmlBuilder:kr,mathmlBuilder:Sr}),Zt({type:"mclass",names:["\\boldsymbol","\\bm"],props:{numArgs:1,greediness:2},handler:function(t,e){var r=t.parser,n=e[0];return{type:"mclass",mode:r.mode,mclass:wr(n),body:[{type:"font",mode:r.mode,font:"boldsymbol",body:n}]}}}),Zt({type:"font",names:["\\rm","\\sf","\\tt","\\bf","\\it"],props:{numArgs:0,allowedInText:!0},handler:function(t,e){var r=t.parser,n=t.funcName,a=t.breakOnTokenText,i=r.mode,o=r.parseExpression(!0,a);return{type:"font",mode:i,font:"math"+n.slice(1),body:{type:"ordgroup",mode:r.mode,body:o}}},htmlBuilder:kr,mathmlBuilder:Sr});var Mr=function(t,e){var r=e.style;"display"===t.size?r=w.DISPLAY:"text"===t.size&&r.size===w.DISPLAY.size?r=w.TEXT:"script"===t.size?r=w.SCRIPT:"scriptscript"===t.size&&(r=w.SCRIPTSCRIPT);var n,a=r.fracNum(),i=r.fracDen();n=e.havingStyle(a);var o=le(t.numer,n,e);if(t.continued){var s=8.5/e.fontMetrics().ptPerEm,h=3.5/e.fontMetrics().ptPerEm;o.height=o.height<s?s:o.height,o.depth=o.depth<h?h:o.depth}n=e.havingStyle(i);var l,m,c,u,d,p,f,g,x,v,y=le(t.denom,n,e);if(t.hasBarLine?(t.barSize?(m=Mt(t.barSize,e),l=Dt.makeLineSpan("frac-line",e,m)):l=Dt.makeLineSpan("frac-line",e),m=l.height,c=l.height):(l=null,m=0,c=e.fontMetrics().defaultRuleThickness),r.size===w.DISPLAY.size?(u=e.fontMetrics().num1,d=m>0?3*c:7*c,p=e.fontMetrics().denom1):(m>0?(u=e.fontMetrics().num2,d=c):(u=e.fontMetrics().num3,d=3*c),p=e.fontMetrics().denom2),l){var b=e.fontMetrics().axisHeight;u-o.depth-(b+.5*m)<d&&(u+=d-(u-o.depth-(b+.5*m))),b-.5*m-(y.height-p)<d&&(p+=d-(b-.5*m-(y.height-p)));var k=-(b-.5*m);f=Dt.makeVList({positionType:"individualShift",children:[{type:"elem",elem:y,shift:p},{type:"elem",elem:l,shift:k},{type:"elem",elem:o,shift:-u}]},e)}else{var S=u-o.depth-(y.height-p);S<d&&(u+=.5*(d-S),p+=.5*(d-S)),f=Dt.makeVList({positionType:"individualShift",children:[{type:"elem",elem:y,shift:p},{type:"elem",elem:o,shift:-u}]},e)}return n=e.havingStyle(r),f.height*=n.sizeMultiplier/e.sizeMultiplier,f.depth*=n.sizeMultiplier/e.sizeMultiplier,g=r.size===w.DISPLAY.size?e.fontMetrics().delim1:e.fontMetrics().delim2,x=null==t.leftDelim?he(e,["mopen"]):er(t.leftDelim,g,!0,e.havingStyle(r),t.mode,["mopen"]),v=t.continued?Dt.makeSpan([]):null==t.rightDelim?he(e,["mclose"]):er(t.rightDelim,g,!0,e.havingStyle(r),t.mode,["mclose"]),Dt.makeSpan(["mord"].concat(n.sizingClasses(e)),[x,Dt.makeSpan(["mfrac"],[f]),v],e)},Tr=function(t,e){var r=new fe.MathNode("mfrac",[we(t.numer,e),we(t.denom,e)]);if(t.hasBarLine){if(t.barSize){var n=Mt(t.barSize,e);r.setAttribute("linethickness",n+"em")}}else r.setAttribute("linethickness","0px");if(null!=t.leftDelim||null!=t.rightDelim){var a=[];if(null!=t.leftDelim){var i=new fe.MathNode("mo",[new fe.TextNode(t.leftDelim)]);i.setAttribute("fence","true"),a.push(i)}if(a.push(r),null!=t.rightDelim){var o=new fe.MathNode("mo",[new fe.TextNode(t.rightDelim)]);o.setAttribute("fence","true"),a.push(o)}return xe(a)}return r};Zt({type:"genfrac",names:["\\cfrac","\\dfrac","\\frac","\\tfrac","\\dbinom","\\binom","\\tbinom","\\\\atopfrac","\\\\bracefrac","\\\\brackfrac"],props:{numArgs:2,greediness:2},handler:function(t,e){var r,n=t.parser,a=t.funcName,i=e[0],o=e[1],s=null,h=null,l="auto";switch(a){case"\\cfrac":case"\\dfrac":case"\\frac":case"\\tfrac":r=!0;break;case"\\\\atopfrac":r=!1;break;case"\\dbinom":case"\\binom":case"\\tbinom":r=!1,s="(",h=")";break;case"\\\\bracefrac":r=!1,s="\\{",h="\\}";break;case"\\\\brackfrac":r=!1,s="[",h="]";break;default:throw new Error("Unrecognized genfrac command")}switch(a){case"\\cfrac":case"\\dfrac":case"\\dbinom":l="display";break;case"\\tfrac":case"\\tbinom":l="text"}return{type:"genfrac",mode:n.mode,continued:"\\cfrac"===a,numer:i,denom:o,hasBarLine:r,leftDelim:s,rightDelim:h,size:l,barSize:null}},htmlBuilder:Mr,mathmlBuilder:Tr}),Zt({type:"infix",names:["\\over","\\choose","\\atop","\\brace","\\brack"],props:{numArgs:0,infix:!0},handler:function(t){var e,r=t.parser,n=t.funcName,a=t.token;switch(n){case"\\over":e="\\frac";break;case"\\choose":e="\\binom";break;case"\\atop":e="\\\\atopfrac";break;case"\\brace":e="\\\\bracefrac";break;case"\\brack":e="\\\\brackfrac";break;default:throw new Error("Unrecognized infix genfrac command")}return{type:"infix",mode:r.mode,replaceWith:e,token:a}}});var Ar=["display","text","script","scriptscript"],Br=function(t){var e=null;return t.length>0&&(e="."===(e=t)?null:e),e};Zt({type:"genfrac",names:["\\genfrac"],props:{numArgs:6,greediness:6,argTypes:["math","math","size","text","math","math"]},handler:function(t,e){var r=t.parser,n=e[4],a=e[5],i=Pt(e[0],"atom");i&&(i=Ft(e[0],"open"));var o=i?Br(i.text):null,s=Pt(e[1],"atom");s&&(s=Ft(e[1],"close"));var h,l=s?Br(s.text):null,m=Ht(e[2],"size"),c=null;h=!!m.isBlank||(c=m.value).number>0;var u="auto",d=Pt(e[3],"ordgroup");if(d){if(d.body.length>0){var p=Ht(d.body[0],"textord");u=Ar[Number(p.text)]}}else d=Ht(e[3],"textord"),u=Ar[Number(d.text)];return{type:"genfrac",mode:r.mode,numer:n,denom:a,continued:!1,hasBarLine:h,barSize:c,leftDelim:o,rightDelim:l,size:u}},htmlBuilder:Mr,mathmlBuilder:Tr}),Zt({type:"infix",names:["\\above"],props:{numArgs:1,argTypes:["size"],infix:!0},handler:function(t,e){var r=t.parser,n=(t.funcName,t.token);return{type:"infix",mode:r.mode,replaceWith:"\\\\abovefrac",size:Ht(e[0],"size").value,token:n}}}),Zt({type:"genfrac",names:["\\\\abovefrac"],props:{numArgs:3,argTypes:["math","size","math"]},handler:function(t,e){var r=t.parser,n=(t.funcName,e[0]),a=function(t){if(!t)throw new Error("Expected non-null, but got "+String(t));return t}(Ht(e[1],"infix").size),i=e[2],o=a.number>0;return{type:"genfrac",mode:r.mode,numer:n,denom:i,continued:!1,hasBarLine:o,barSize:a,leftDelim:null,rightDelim:null,size:"auto"}},htmlBuilder:Mr,mathmlBuilder:Tr});var Cr=function(t,e){var r,n,a=e.style,i=Pt(t,"supsub");i?(r=i.sup?le(i.sup,e.havingStyle(a.sup()),e):le(i.sub,e.havingStyle(a.sub()),e),n=Ht(i.base,"horizBrace")):n=Ht(t,"horizBrace");var o,s=le(n.base,e.havingBaseStyle(w.DISPLAY)),h=Ne(n,e);if(n.isOver?(o=Dt.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:s},{type:"kern",size:.1},{type:"elem",elem:h}]},e)).children[0].children[0].children[1].classes.push("svg-align"):(o=Dt.makeVList({positionType:"bottom",positionData:s.depth+.1+h.height,children:[{type:"elem",elem:h},{type:"kern",size:.1},{type:"elem",elem:s}]},e)).children[0].children[0].children[0].classes.push("svg-align"),r){var l=Dt.makeSpan(["mord",n.isOver?"mover":"munder"],[o],e);o=n.isOver?Dt.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:l},{type:"kern",size:.2},{type:"elem",elem:r}]},e):Dt.makeVList({positionType:"bottom",positionData:l.depth+.2+r.height+r.depth,children:[{type:"elem",elem:r},{type:"kern",size:.2},{type:"elem",elem:l}]},e)}return Dt.makeSpan(["mord",n.isOver?"mover":"munder"],[o],e)};Zt({type:"horizBrace",names:["\\overbrace","\\underbrace"],props:{numArgs:1},handler:function(t,e){var r=t.parser,n=t.funcName;return{type:"horizBrace",mode:r.mode,label:n,isOver:/^\\over/.test(n),base:e[0]}},htmlBuilder:Cr,mathmlBuilder:function(t,e){var r=Ce(t.label);return new fe.MathNode(t.isOver?"mover":"munder",[we(t.base,e),r])}}),Zt({type:"href",names:["\\href"],props:{numArgs:2,argTypes:["url","original"],allowedInText:!0},handler:function(t,e){var r=t.parser,n=e[1],a=Ht(e[0],"url").url;return{type:"href",mode:r.mode,href:a,body:Jt(n)}},htmlBuilder:function(t,e){var r=ae(t.body,e,!1);return Dt.makeAnchor(t.href,[],r,e)},mathmlBuilder:function(t,e){var r=be(t.body,e);return r instanceof de||(r=new de("mrow",[r])),r.setAttribute("href",t.href),r}}),Zt({type:"href",names:["\\url"],props:{numArgs:1,argTypes:["url"],allowedInText:!0},handler:function(t,e){for(var r=t.parser,n=Ht(e[0],"url").url,a=[],i=0;i<n.length;i++){var o=n[i];"~"===o&&(o="\\textasciitilde"),a.push({type:"textord",mode:"text",text:o})}var s={type:"text",mode:r.mode,font:"\\texttt",body:a};return{type:"href",mode:r.mode,href:n,body:Jt(s)}}}),Zt({type:"htmlmathml",names:["\\html@mathml"],props:{numArgs:2,allowedInText:!0},handler:function(t,e){return{type:"htmlmathml",mode:t.parser.mode,html:Jt(e[0]),mathml:Jt(e[1])}},htmlBuilder:function(t,e){var r=ae(t.html,e,!1);return Dt.makeFragment(r)},mathmlBuilder:function(t,e){return be(t.mathml,e)}}),Zt({type:"kern",names:["\\kern","\\mkern","\\hskip","\\mskip"],props:{numArgs:1,argTypes:["size"],allowedInText:!0},handler:function(t,e){var r=t.parser,n=t.funcName,a=Ht(e[0],"size");if(r.settings.strict){var i="m"===n[1],o="mu"===a.value.unit;i?(o||r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+n+" supports only mu units, not "+a.value.unit+" units"),"math"!==r.mode&&r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+n+" works only in math mode")):o&&r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+n+" doesn't support mu units")}return{type:"kern",mode:r.mode,dimension:a.value}},htmlBuilder:function(t,e){return Dt.makeGlue(t.dimension,e)},mathmlBuilder:function(t,e){var r=Mt(t.dimension,e);return new fe.SpaceNode(r)}}),Zt({type:"lap",names:["\\mathllap","\\mathrlap","\\mathclap"],props:{numArgs:1,allowedInText:!0},handler:function(t,e){var r=t.parser,n=t.funcName,a=e[0];return{type:"lap",mode:r.mode,alignment:n.slice(5),body:a}},htmlBuilder:function(t,e){var r;"clap"===t.alignment?(r=Dt.makeSpan([],[le(t.body,e)]),r=Dt.makeSpan(["inner"],[r],e)):r=Dt.makeSpan(["inner"],[le(t.body,e)]);var n=Dt.makeSpan(["fix"],[]),a=Dt.makeSpan([t.alignment],[r,n],e),i=Dt.makeSpan(["strut"]);return i.style.height=a.height+a.depth+"em",i.style.verticalAlign=-a.depth+"em",a.children.unshift(i),a=Dt.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:a}]},e),Dt.makeSpan(["mord"],[a],e)},mathmlBuilder:function(t,e){var r=new fe.MathNode("mpadded",[we(t.body,e)]);if("rlap"!==t.alignment){var n="llap"===t.alignment?"-1":"-0.5";r.setAttribute("lspace",n+"width")}return r.setAttribute("width","0px"),r}}),Zt({type:"styling",names:["\\(","$"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1,consumeMode:"math"},handler:function(t,e){var r=t.funcName,n=t.parser,a=n.mode;n.switchMode("math");var i="\\("===r?"\\)":"$",o=n.parseExpression(!1,i);return n.expect(i,!1),n.switchMode(a),n.consume(),{type:"styling",mode:n.mode,style:"text",body:o}}}),Zt({type:"text",names:["\\)","\\]"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler:function(t,e){throw new o("Mismatched "+t.funcName)}});var Nr=function(t,e){switch(e.style.size){case w.DISPLAY.size:return t.display;case w.TEXT.size:return t.text;case w.SCRIPT.size:return t.script;case w.SCRIPTSCRIPT.size:return t.scriptscript;default:return t.text}};Zt({type:"mathchoice",names:["\\mathchoice"],props:{numArgs:4},handler:function(t,e){return{type:"mathchoice",mode:t.parser.mode,display:Jt(e[0]),text:Jt(e[1]),script:Jt(e[2]),scriptscript:Jt(e[3])}},htmlBuilder:function(t,e){var r=Nr(t,e),n=ae(r,e,!1);return Dt.makeFragment(n)},mathmlBuilder:function(t,e){var r=Nr(t,e);return be(r,e)}});var qr=function(t,e){var r,n,a,i=!1,o=Pt(t,"supsub");o?(r=o.sup,n=o.sub,a=Ht(o.base,"op"),i=!0):a=Ht(t,"op");var s,h=e.style,l=!1;if(h.size===w.DISPLAY.size&&a.symbol&&!c.contains(["\\smallint"],a.name)&&(l=!0),a.symbol){var m=l?"Size2-Regular":"Size1-Regular",u="";if("\\oiint"!==a.name&&"\\oiiint"!==a.name||(u=a.name.substr(1),a.name="oiint"===u?"\\iint":"\\iiint"),s=Dt.makeSymbol(a.name,m,"math",e,["mop","op-symbol",l?"large-op":"small-op"]),u.length>0){var d=s.italic,p=Dt.staticSvg(u+"Size"+(l?"2":"1"),e);s=Dt.makeVList({positionType:"individualShift",children:[{type:"elem",elem:s,shift:0},{type:"elem",elem:p,shift:l?.08:0}]},e),a.name="\\"+u,s.classes.unshift("mop"),s.italic=d}}else if(a.body){var f=ae(a.body,e,!0);1===f.length&&f[0]instanceof I?(s=f[0]).classes[0]="mop":s=Dt.makeSpan(["mop"],Dt.tryCombineChars(f),e)}else{for(var g=[],x=1;x<a.name.length;x++)g.push(Dt.mathsym(a.name[x],a.mode));s=Dt.makeSpan(["mop"],g,e)}var v=0,y=0;if((s instanceof I||"\\oiint"===a.name||"\\oiiint"===a.name)&&!a.suppressBaseShift&&(v=(s.height-s.depth)/2-e.fontMetrics().axisHeight,y=s.italic),i){var b,k,S;if(s=Dt.makeSpan([],[s]),r){var z=le(r,e.havingStyle(h.sup()),e);k={elem:z,kern:Math.max(e.fontMetrics().bigOpSpacing1,e.fontMetrics().bigOpSpacing3-z.depth)}}if(n){var M=le(n,e.havingStyle(h.sub()),e);b={elem:M,kern:Math.max(e.fontMetrics().bigOpSpacing2,e.fontMetrics().bigOpSpacing4-M.height)}}if(k&&b){var T=e.fontMetrics().bigOpSpacing5+b.elem.height+b.elem.depth+b.kern+s.depth+v;S=Dt.makeVList({positionType:"bottom",positionData:T,children:[{type:"kern",size:e.fontMetrics().bigOpSpacing5},{type:"elem",elem:b.elem,marginLeft:-y+"em"},{type:"kern",size:b.kern},{type:"elem",elem:s},{type:"kern",size:k.kern},{type:"elem",elem:k.elem,marginLeft:y+"em"},{type:"kern",size:e.fontMetrics().bigOpSpacing5}]},e)}else if(b){var A=s.height-v;S=Dt.makeVList({positionType:"top",positionData:A,children:[{type:"kern",size:e.fontMetrics().bigOpSpacing5},{type:"elem",elem:b.elem,marginLeft:-y+"em"},{type:"kern",size:b.kern},{type:"elem",elem:s}]},e)}else{if(!k)return s;var B=s.depth+v;S=Dt.makeVList({positionType:"bottom",positionData:B,children:[{type:"elem",elem:s},{type:"kern",size:k.kern},{type:"elem",elem:k.elem,marginLeft:y+"em"},{type:"kern",size:e.fontMetrics().bigOpSpacing5}]},e)}return Dt.makeSpan(["mop","op-limits"],[S],e)}return v&&(s.style.position="relative",s.style.top=v+"em"),s},Er=function(t,e){var r;if(t.symbol)r=new de("mo",[ge(t.name,t.mode)]);else{if(!t.body)return ue([r=new de("mi",[new pe(t.name.slice(1))]),new de("mo",[ge("\u2061","text")])]);r=new de("mo",ye(t.body,e))}return r},Or={"\u220f":"\\prod","\u2210":"\\coprod","\u2211":"\\sum","\u22c0":"\\bigwedge","\u22c1":"\\bigvee","\u22c2":"\\bigcap","\u22c3":"\\bigcup","\u2a00":"\\bigodot","\u2a01":"\\bigoplus","\u2a02":"\\bigotimes","\u2a04":"\\biguplus","\u2a06":"\\bigsqcup"};Zt({type:"op",names:["\\coprod","\\bigvee","\\bigwedge","\\biguplus","\\bigcap","\\bigcup","\\intop","\\prod","\\sum","\\bigotimes","\\bigoplus","\\bigodot","\\bigsqcup","\\smallint","\u220f","\u2210","\u2211","\u22c0","\u22c1","\u22c2","\u22c3","\u2a00","\u2a01","\u2a02","\u2a04","\u2a06"],props:{numArgs:0},handler:function(t,e){var r=t.parser,n=t.funcName;return 1===n.length&&(n=Or[n]),{type:"op",mode:r.mode,limits:!0,symbol:!0,name:n}},htmlBuilder:qr,mathmlBuilder:Er}),Zt({type:"op",names:["\\mathop"],props:{numArgs:1},handler:function(t,e){var r=t.parser,n=e[0];return{type:"op",mode:r.mode,limits:!1,symbol:!1,body:Jt(n)}},htmlBuilder:qr,mathmlBuilder:Er});var Ir={"\u222b":"\\int","\u222c":"\\iint","\u222d":"\\iiint","\u222e":"\\oint","\u222f":"\\oiint","\u2230":"\\oiiint"};function Rr(t,e,r){for(var n=ae(t,e,!1),a=e.sizeMultiplier/r.sizeMultiplier,i=0;i<n.length;i++){var o=n[i].classes.indexOf("sizing");o<0?Array.prototype.push.apply(n[i].classes,e.sizingClasses(r)):n[i].classes[o+1]==="reset-size"+e.size&&(n[i].classes[o+1]="reset-size"+r.size),n[i].height*=a,n[i].depth*=a}return Dt.makeFragment(n)}Zt({type:"op",names:["\\arcsin","\\arccos","\\arctan","\\arctg","\\arcctg","\\arg","\\ch","\\cos","\\cosec","\\cosh","\\cot","\\cotg","\\coth","\\csc","\\ctg","\\cth","\\deg","\\dim","\\exp","\\hom","\\ker","\\lg","\\ln","\\log","\\sec","\\sin","\\sinh","\\sh","\\tan","\\tanh","\\tg","\\th"],props:{numArgs:0},handler:function(t){var e=t.parser,r=t.funcName;return{type:"op",mode:e.mode,limits:!1,symbol:!1,name:r}},htmlBuilder:qr,mathmlBuilder:Er}),Zt({type:"op",names:["\\det","\\gcd","\\inf","\\lim","\\max","\\min","\\Pr","\\sup"],props:{numArgs:0},handler:function(t){var e=t.parser,r=t.funcName;return{type:"op",mode:e.mode,limits:!0,symbol:!1,name:r}},htmlBuilder:qr,mathmlBuilder:Er}),Zt({type:"op",names:["\\int","\\iint","\\iiint","\\oint","\\oiint","\\oiiint","\u222b","\u222c","\u222d","\u222e","\u222f","\u2230"],props:{numArgs:0},handler:function(t){var e=t.parser,r=t.funcName;return 1===r.length&&(r=Ir[r]),{type:"op",mode:e.mode,limits:!1,symbol:!0,name:r}},htmlBuilder:qr,mathmlBuilder:Er}),Zt({type:"operatorname",names:["\\operatorname"],props:{numArgs:1},handler:function(t,e){var r=t.parser,n=e[0];return{type:"operatorname",mode:r.mode,body:Jt(n)}},htmlBuilder:function(t,e){if(t.body.length>0){for(var r=t.body.map(function(t){var e=t.text;return"string"==typeof e?{type:"textord",mode:t.mode,text:e}:t}),n=ae(r,e.withFont("mathrm"),!0),a=0;a<n.length;a++){var i=n[a];i instanceof I&&(i.text=i.text.replace(/\u2212/,"-").replace(/\u2217/,"*"))}return Dt.makeSpan(["mop"],n,e)}return Dt.makeSpan(["mop"],[],e)},mathmlBuilder:function(t,e){for(var r=ye(t.body,e.withFont("mathrm")),n=!0,a=0;a<r.length;a++){var i=r[a];if(i instanceof fe.SpaceNode);else if(i instanceof fe.MathNode)switch(i.type){case"mi":case"mn":case"ms":case"mspace":case"mtext":break;case"mo":var o=i.children[0];1===i.children.length&&o instanceof fe.TextNode?o.text=o.text.replace(/\u2212/,"-").replace(/\u2217/,"*"):n=!1;break;default:n=!1}else n=!1}if(n){var s=r.map(function(t){return t.toText()}).join("");r=[new fe.TextNode(s)]}var h=new fe.MathNode("mi",r);h.setAttribute("mathvariant","normal");var l=new fe.MathNode("mo",[ge("\u2061","text")]);return fe.newDocumentFragment([h,l])}}),Kt({type:"ordgroup",htmlBuilder:function(t,e){return t.semisimple?Dt.makeFragment(ae(t.body,e,!1)):Dt.makeSpan(["mord"],ae(t.body,e,!0),e)},mathmlBuilder:function(t,e){return be(t.body,e)}}),Zt({type:"overline",names:["\\overline"],props:{numArgs:1},handler:function(t,e){var r=t.parser,n=e[0];return{type:"overline",mode:r.mode,body:n}},htmlBuilder:function(t,e){var r=le(t.body,e.havingCrampedStyle()),n=Dt.makeLineSpan("overline-line",e),a=Dt.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r},{type:"kern",size:3*n.height},{type:"elem",elem:n},{type:"kern",size:n.height}]},e);return Dt.makeSpan(["mord","overline"],[a],e)},mathmlBuilder:function(t,e){var r=new fe.MathNode("mo",[new fe.TextNode("\u203e")]);r.setAttribute("stretchy","true");var n=new fe.MathNode("mover",[we(t.body,e),r]);return n.setAttribute("accent","true"),n}}),Zt({type:"phantom",names:["\\phantom"],props:{numArgs:1,allowedInText:!0},handler:function(t,e){var r=t.parser,n=e[0];return{type:"phantom",mode:r.mode,body:Jt(n)}},htmlBuilder:function(t,e){var r=ae(t.body,e.withPhantom(),!1);return Dt.makeFragment(r)},mathmlBuilder:function(t,e){var r=ye(t.body,e);return new fe.MathNode("mphantom",r)}}),Zt({type:"hphantom",names:["\\hphantom"],props:{numArgs:1,allowedInText:!0},handler:function(t,e){var r=t.parser,n=e[0];return{type:"hphantom",mode:r.mode,body:n}},htmlBuilder:function(t,e){var r=Dt.makeSpan([],[le(t.body,e.withPhantom())]);if(r.height=0,r.depth=0,r.children)for(var n=0;n<r.children.length;n++)r.children[n].height=0,r.children[n].depth=0;return r=Dt.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r}]},e),Dt.makeSpan(["mord"],[r],e)},mathmlBuilder:function(t,e){var r=ye(Jt(t.body),e),n=new fe.MathNode("mphantom",r);return n.setAttribute("height","0px"),n}}),Zt({type:"vphantom",names:["\\vphantom"],props:{numArgs:1,allowedInText:!0},handler:function(t,e){var r=t.parser,n=e[0];return{type:"vphantom",mode:r.mode,body:n}},htmlBuilder:function(t,e){var r=Dt.makeSpan(["inner"],[le(t.body,e.withPhantom())]),n=Dt.makeSpan(["fix"],[]);return Dt.makeSpan(["mord","rlap"],[r,n],e)},mathmlBuilder:function(t,e){var r=ye(Jt(t.body),e),n=new fe.MathNode("mphantom",r);return n.setAttribute("width","0px"),n}});var Lr=["\\tiny","\\sixptsize","\\scriptsize","\\footnotesize","\\small","\\normalsize","\\large","\\Large","\\LARGE","\\huge","\\Huge"],Dr=function(t,e){var r=e.havingSize(t.size);return Rr(t.body,r,e)};Zt({type:"sizing",names:Lr,props:{numArgs:0,allowedInText:!0},handler:function(t,e){var r=t.breakOnTokenText,n=t.funcName,a=t.parser,i=a.parseExpression(!1,r);return{type:"sizing",mode:a.mode,size:Lr.indexOf(n)+1,body:i}},htmlBuilder:Dr,mathmlBuilder:function(t,e){var r=e.havingSize(t.size),n=ye(t.body,r),a=new fe.MathNode("mstyle",n);return a.setAttribute("mathsize",r.sizeMultiplier+"em"),a}}),Zt({type:"raisebox",names:["\\raisebox"],props:{numArgs:2,argTypes:["size","text"],allowedInText:!0},handler:function(t,e){var r=t.parser,n=Ht(e[0],"size").value,a=e[1];return{type:"raisebox",mode:r.mode,dy:n,body:a}},htmlBuilder:function(t,e){var r={type:"text",mode:t.mode,body:Jt(t.body),font:"mathrm"},n={type:"sizing",mode:t.mode,body:[r],size:6},a=Dr(n,e),i=Mt(t.dy,e);return Dt.makeVList({positionType:"shift",positionData:-i,children:[{type:"elem",elem:a}]},e)},mathmlBuilder:function(t,e){var r=new fe.MathNode("mpadded",[we(t.body,e)]),n=t.dy.number+t.dy.unit;return r.setAttribute("voffset",n),r}}),Zt({type:"rule",names:["\\rule"],props:{numArgs:2,numOptionalArgs:1,argTypes:["size","size","size"]},handler:function(t,e,r){var n=t.parser,a=r[0],i=Ht(e[0],"size"),o=Ht(e[1],"size");return{type:"rule",mode:n.mode,shift:a&&Ht(a,"size").value,width:i.value,height:o.value}},htmlBuilder:function(t,e){var r=Dt.makeSpan(["mord","rule"],[],e),n=0;t.shift&&(n=Mt(t.shift,e));var a=Mt(t.width,e),i=Mt(t.height,e);return r.style.borderRightWidth=a+"em",r.style.borderTopWidth=i+"em",r.style.bottom=n+"em",r.width=a,r.height=i+n,r.depth=-n,r.maxFontSize=1.125*i*e.sizeMultiplier,r},mathmlBuilder:function(t,e){return new fe.MathNode("mrow")}}),Zt({type:"smash",names:["\\smash"],props:{numArgs:1,numOptionalArgs:1,allowedInText:!0},handler:function(t,e,r){var n=t.parser,a=!1,i=!1,o=r[0]&&Ht(r[0],"ordgroup");if(o)for(var s="",h=0;h<o.body.length;++h){if("t"===(s=o.body[h].text))a=!0;else{if("b"!==s){a=!1,i=!1;break}i=!0}}else a=!0,i=!0;var l=e[0];return{type:"smash",mode:n.mode,body:l,smashHeight:a,smashDepth:i}},htmlBuilder:function(t,e){var r=Dt.makeSpan([],[le(t.body,e)]);if(!t.smashHeight&&!t.smashDepth)return r;if(t.smashHeight&&(r.height=0,r.children))for(var n=0;n<r.children.length;n++)r.children[n].height=0;if(t.smashDepth&&(r.depth=0,r.children))for(var a=0;a<r.children.length;a++)r.children[a].depth=0;var i=Dt.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r}]},e);return Dt.makeSpan(["mord"],[i],e)},mathmlBuilder:function(t,e){var r=new fe.MathNode("mpadded",[we(t.body,e)]);return t.smashHeight&&r.setAttribute("height","0px"),t.smashDepth&&r.setAttribute("depth","0px"),r}}),Zt({type:"sqrt",names:["\\sqrt"],props:{numArgs:1,numOptionalArgs:1},handler:function(t,e,r){var n=t.parser,a=r[0],i=e[0];return{type:"sqrt",mode:n.mode,body:i,index:a}},htmlBuilder:function(t,e){var r=le(t.body,e.havingCrampedStyle());0===r.height&&(r.height=e.fontMetrics().xHeight),r=Dt.wrapFragment(r,e);var n=e.fontMetrics().defaultRuleThickness,a=n;e.style.id<w.TEXT.id&&(a=e.fontMetrics().xHeight);var i=n+a/4,o=r.height+r.depth+i+n,s=Qe(o,e),h=s.span,l=s.ruleWidth,m=s.advanceWidth,c=h.height-l;c>r.height+r.depth+i&&(i=(i+c-r.height-r.depth)/2);var u=h.height-r.height-i-l;r.style.paddingLeft=m+"em";var d=Dt.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r,wrapperClasses:["svg-align"]},{type:"kern",size:-(r.height+u)},{type:"elem",elem:h},{type:"kern",size:l}]},e);if(t.index){var p=e.havingStyle(w.SCRIPTSCRIPT),f=le(t.index,p,e),g=.6*(d.height-d.depth),x=Dt.makeVList({positionType:"shift",positionData:-g,children:[{type:"elem",elem:f}]},e),v=Dt.makeSpan(["root"],[x]);return Dt.makeSpan(["mord","sqrt"],[v,d],e)}return Dt.makeSpan(["mord","sqrt"],[d],e)},mathmlBuilder:function(t,e){var r=t.body,n=t.index;return n?new fe.MathNode("mroot",[we(r,e),we(n,e)]):new fe.MathNode("msqrt",[we(r,e)])}});var Hr={display:w.DISPLAY,text:w.TEXT,script:w.SCRIPT,scriptscript:w.SCRIPTSCRIPT};Zt({type:"styling",names:["\\displaystyle","\\textstyle","\\scriptstyle","\\scriptscriptstyle"],props:{numArgs:0,allowedInText:!0},handler:function(t,e){var r=t.breakOnTokenText,n=t.funcName,a=t.parser,i=a.parseExpression(!0,r),o=n.slice(1,n.length-5);return{type:"styling",mode:a.mode,style:o,body:i}},htmlBuilder:function(t,e){var r=Hr[t.style],n=e.havingStyle(r).withFont("");return Rr(t.body,n,e)},mathmlBuilder:function(t,e){var r={display:w.DISPLAY,text:w.TEXT,script:w.SCRIPT,scriptscript:w.SCRIPTSCRIPT}[t.style],n=e.havingStyle(r),a=ye(t.body,n),i=new fe.MathNode("mstyle",a),o={display:["0","true"],text:["0","false"],script:["1","false"],scriptscript:["2","false"]}[t.style];return i.setAttribute("scriptlevel",o[0]),i.setAttribute("displaystyle",o[1]),i}});Kt({type:"supsub",htmlBuilder:function(t,e){var r=function(t,e){var r=t.base;return r?"op"===r.type?r.limits&&(e.style.size===w.DISPLAY.size||r.alwaysHandleSupSub)?qr:null:"accent"===r.type?c.isCharacterBox(r.base)?qe:null:"horizBrace"===r.type&&!t.sub===r.isOver?Cr:null:null}(t,e);if(r)return r(t,e);var n,a,i,o=t.base,s=t.sup,h=t.sub,l=le(o,e),m=e.fontMetrics(),u=0,d=0,p=o&&c.isCharacterBox(o);if(s){var f=e.havingStyle(e.style.sup());n=le(s,f,e),p||(u=l.height-f.fontMetrics().supDrop*f.sizeMultiplier/e.sizeMultiplier)}if(h){var g=e.havingStyle(e.style.sub());a=le(h,g,e),p||(d=l.depth+g.fontMetrics().subDrop*g.sizeMultiplier/e.sizeMultiplier)}i=e.style===w.DISPLAY?m.sup1:e.style.cramped?m.sup3:m.sup2;var x,v=e.sizeMultiplier,y=.5/m.ptPerEm/v+"em",b=null;if(a){var k=t.base&&"op"===t.base.type&&t.base.name&&("\\oiint"===t.base.name||"\\oiiint"===t.base.name);(l instanceof I||k)&&(b=-l.italic+"em")}if(n&&a){u=Math.max(u,i,n.depth+.25*m.xHeight),d=Math.max(d,m.sub2);var S=4*m.defaultRuleThickness;if(u-n.depth-(a.height-d)<S){d=S-(u-n.depth)+a.height;var z=.8*m.xHeight-(u-n.depth);z>0&&(u+=z,d-=z)}var M=[{type:"elem",elem:a,shift:d,marginRight:y,marginLeft:b},{type:"elem",elem:n,shift:-u,marginRight:y}];x=Dt.makeVList({positionType:"individualShift",children:M},e)}else if(a){d=Math.max(d,m.sub1,a.height-.8*m.xHeight);var T=[{type:"elem",elem:a,marginLeft:b,marginRight:y}];x=Dt.makeVList({positionType:"shift",positionData:d,children:T},e)}else{if(!n)throw new Error("supsub must have either sup or sub.");u=Math.max(u,i,n.depth+.25*m.xHeight),x=Dt.makeVList({positionType:"shift",positionData:-u,children:[{type:"elem",elem:n,marginRight:y}]},e)}var A=se(l,"right")||"mord";return Dt.makeSpan([A],[l,Dt.makeSpan(["msupsub"],[x])],e)},mathmlBuilder:function(t,e){var r,n=!1,a=Pt(t.base,"horizBrace");a&&!!t.sup===a.isOver&&(n=!0,r=a.isOver);var i,o=[we(t.base,e)];if(t.sub&&o.push(we(t.sub,e)),t.sup&&o.push(we(t.sup,e)),n)i=r?"mover":"munder";else if(t.sub)if(t.sup){var s=t.base;i=s&&"op"===s.type&&s.limits&&e.style===w.DISPLAY?"munderover":"msubsup"}else{var h=t.base;i=h&&"op"===h.type&&h.limits&&e.style===w.DISPLAY?"munder":"msub"}else{var l=t.base;i=l&&"op"===l.type&&l.limits&&e.style===w.DISPLAY?"mover":"msup"}return new fe.MathNode(i,o)}}),Kt({type:"atom",htmlBuilder:function(t,e){return Dt.mathsym(t.text,t.mode,e,["m"+t.family])},mathmlBuilder:function(t,e){var r=new fe.MathNode("mo",[ge(t.text,t.mode)]);if("bin"===t.family){var n=ve(t,e);"bold-italic"===n&&r.setAttribute("mathvariant",n)}else"punct"===t.family&&r.setAttribute("separator","true");return r}});var Pr={mi:"italic",mn:"normal",mtext:"normal"};Kt({type:"mathord",htmlBuilder:function(t,e){return Dt.makeOrd(t,e,"mathord")},mathmlBuilder:function(t,e){var r=new fe.MathNode("mi",[ge(t.text,t.mode,e)]),n=ve(t,e)||"italic";return n!==Pr[r.type]&&r.setAttribute("mathvariant",n),r}}),Kt({type:"textord",htmlBuilder:function(t,e){return Dt.makeOrd(t,e,"textord")},mathmlBuilder:function(t,e){var r,n=ge(t.text,t.mode,e),a=ve(t,e)||"normal";return r="text"===t.mode?new fe.MathNode("mtext",[n]):/[0-9]/.test(t.text)?new fe.MathNode("mn",[n]):"\\prime"===t.text?new fe.MathNode("mo",[n]):new fe.MathNode("mi",[n]),a!==Pr[r.type]&&r.setAttribute("mathvariant",a),r}});var Fr={"\\nobreak":"nobreak","\\allowbreak":"allowbreak"},Vr={" ":{},"\\ ":{},"~":{className:"nobreak"},"\\space":{},"\\nobreakspace":{className:"nobreak"}};Kt({type:"spacing",htmlBuilder:function(t,e){if(Vr.hasOwnProperty(t.text)){var r=Vr[t.text].className||"";if("text"===t.mode){var n=Dt.makeOrd(t,e,"textord");return n.classes.push(r),n}return Dt.makeSpan(["mspace",r],[Dt.mathsym(t.text,t.mode,e)],e)}if(Fr.hasOwnProperty(t.text))return Dt.makeSpan(["mspace",Fr[t.text]],[],e);throw new o('Unknown type of space "'+t.text+'"')},mathmlBuilder:function(t,e){if(!Vr.hasOwnProperty(t.text)){if(Fr.hasOwnProperty(t.text))return new fe.MathNode("mspace");throw new o('Unknown type of space "'+t.text+'"')}return new fe.MathNode("mtext",[new fe.TextNode("\xa0")])}}),Kt({type:"tag",mathmlBuilder:function(t,e){var r=new fe.MathNode("mtable",[new fe.MathNode("mlabeledtr",[new fe.MathNode("mtd",[be(t.tag,e)]),new fe.MathNode("mtd",[be(t.body,e)])])]);return r.setAttribute("side","right"),r}});var Ur={"\\text":void 0,"\\textrm":"textrm","\\textsf":"textsf","\\texttt":"texttt","\\textnormal":"textrm"},Gr={"\\textbf":"textbf"},Xr={"\\textit":"textit"},Yr=function(t,e){var r=t.font;return r?Ur[r]?e.withTextFontFamily(Ur[r]):Gr[r]?e.withTextFontWeight(Gr[r]):e.withTextFontShape(Xr[r]):e};Zt({type:"text",names:["\\text","\\textrm","\\textsf","\\texttt","\\textnormal","\\textbf","\\textit"],props:{numArgs:1,argTypes:["text"],greediness:2,allowedInText:!0,consumeMode:"text"},handler:function(t,e){var r=t.parser,n=t.funcName,a=e[0];return{type:"text",mode:r.mode,body:Jt(a),font:n}},htmlBuilder:function(t,e){var r=Yr(t,e),n=ae(t.body,r,!0);return Dt.makeSpan(["mord","text"],Dt.tryCombineChars(n),r)},mathmlBuilder:function(t,e){var r=Yr(t,e);return be(t.body,r)}}),Zt({type:"underline",names:["\\underline"],props:{numArgs:1,allowedInText:!0},handler:function(t,e){return{type:"underline",mode:t.parser.mode,body:e[0]}},htmlBuilder:function(t,e){var r=le(t.body,e),n=Dt.makeLineSpan("underline-line",e),a=Dt.makeVList({positionType:"top",positionData:r.height,children:[{type:"kern",size:n.height},{type:"elem",elem:n},{type:"kern",size:3*n.height},{type:"elem",elem:r}]},e);return Dt.makeSpan(["mord","underline"],[a],e)},mathmlBuilder:function(t,e){var r=new fe.MathNode("mo",[new fe.TextNode("\u203e")]);r.setAttribute("stretchy","true");var n=new fe.MathNode("munder",[we(t.body,e),r]);return n.setAttribute("accentunder","true"),n}}),Zt({type:"verb",names:["\\verb"],props:{numArgs:0,allowedInText:!0},handler:function(t,e,r){throw new o("\\verb ended by end of line instead of matching delimiter")},htmlBuilder:function(t,e){for(var r=_r(t),n=[],a=e.havingStyle(e.style.text()),i=0;i<r.length;i++){var o=r[i];"~"===o&&(o="\\textasciitilde"),n.push(Dt.makeSymbol(o,"Typewriter-Regular",t.mode,a,["mord","texttt"]))}return Dt.makeSpan(["mord","text"].concat(a.sizingClasses(e)),Dt.tryCombineChars(n),a)},mathmlBuilder:function(t,e){var r=new fe.TextNode(_r(t)),n=new fe.MathNode("mtext",[r]);return n.setAttribute("mathvariant","monospace"),n}});var _r=function(t){return t.body.replace(/ /g,t.star?"\u2423":"\xa0")},Wr=Wt,jr=new RegExp("^(\\\\[a-zA-Z@]+)[ \r\n\t]*$"),$r=new RegExp("[\u0300-\u036f]+$"),Zr="([ \r\n\t]+)|([!-\\[\\]-\u2027\u202a-\ud7ff\uf900-\uffff][\u0300-\u036f]*|[\ud800-\udbff][\udc00-\udfff][\u0300-\u036f]*|\\\\verb\\*([^]).*?\\3|\\\\verb([^*a-zA-Z]).*?\\4|\\\\[a-zA-Z@]+[ \r\n\t]*|\\\\[^\ud800-\udfff])",Kr=function(){function t(t,e){this.input=void 0,this.settings=void 0,this.tokenRegex=void 0,this.catcodes=void 0,this.input=t,this.settings=e,this.tokenRegex=new RegExp(Zr,"g"),this.catcodes={"%":14}}var e=t.prototype;return e.setCatcode=function(t,e){this.catcodes[t]=e},e.lex=function(){var t=this.input,e=this.tokenRegex.lastIndex;if(e===t.length)return new a("EOF",new n(this,e,e));var r=this.tokenRegex.exec(t);if(null===r||r.index!==e)throw new o("Unexpected character: '"+t[e]+"'",new a(t[e],new n(this,e,e+1)));var i=r[2]||" ";if(14===this.catcodes[i]){var s=t.indexOf("\n",this.tokenRegex.lastIndex);return-1===s?(this.tokenRegex.lastIndex=t.length,this.settings.reportNonstrict("commentAtEnd","% comment has no terminating newline; LaTeX would fail because of commenting the end of math mode (e.g. $)")):this.tokenRegex.lastIndex=s+1,this.lex()}var h=i.match(jr);return h&&(i=h[1]),new a(i,new n(this,e,this.tokenRegex.lastIndex))},t}(),Jr=function(){function t(t,e){void 0===t&&(t={}),void 0===e&&(e={}),this.current=void 0,this.builtins=void 0,this.undefStack=void 0,this.current=e,this.builtins=t,this.undefStack=[]}var e=t.prototype;return e.beginGroup=function(){this.undefStack.push({})},e.endGroup=function(){if(0===this.undefStack.length)throw new o("Unbalanced namespace destruction: attempt to pop global namespace; please report this as a bug");var t=this.undefStack.pop();for(var e in t)t.hasOwnProperty(e)&&(void 0===t[e]?delete this.current[e]:this.current[e]=t[e])},e.has=function(t){return this.current.hasOwnProperty(t)||this.builtins.hasOwnProperty(t)},e.get=function(t){return this.current.hasOwnProperty(t)?this.current[t]:this.builtins[t]},e.set=function(t,e,r){if(void 0===r&&(r=!1),r){for(var n=0;n<this.undefStack.length;n++)delete this.undefStack[n][t];this.undefStack.length>0&&(this.undefStack[this.undefStack.length-1][t]=e)}else{var a=this.undefStack[this.undefStack.length-1];a&&!a.hasOwnProperty(t)&&(a[t]=this.current[t])}this.current[t]=e},t}(),Qr={},tn=Qr;function en(t,e){Qr[t]=e}en("\\@firstoftwo",function(t){return{tokens:t.consumeArgs(2)[0],numArgs:0}}),en("\\@secondoftwo",function(t){return{tokens:t.consumeArgs(2)[1],numArgs:0}}),en("\\@ifnextchar",function(t){var e=t.consumeArgs(3),r=t.future();return 1===e[0].length&&e[0][0].text===r.text?{tokens:e[1],numArgs:0}:{tokens:e[2],numArgs:0}}),en("\\@ifstar","\\@ifnextchar *{\\@firstoftwo{#1}}"),en("\\TextOrMath",function(t){var e=t.consumeArgs(2);return"text"===t.mode?{tokens:e[0],numArgs:0}:{tokens:e[1],numArgs:0}});var rn={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};en("\\char",function(t){var e,r=t.popToken(),n="";if("'"===r.text)e=8,r=t.popToken();else if('"'===r.text)e=16,r=t.popToken();else if("`"===r.text)if("\\"===(r=t.popToken()).text[0])n=r.text.charCodeAt(1);else{if("EOF"===r.text)throw new o("\\char` missing argument");n=r.text.charCodeAt(0)}else e=10;if(e){if(null==(n=rn[r.text])||n>=e)throw new o("Invalid base-"+e+" digit "+r.text);for(var a;null!=(a=rn[t.future().text])&&a<e;)n*=e,n+=a,t.popToken()}return"\\@char{"+n+"}"});var nn=function(t,e){var r=t.consumeArgs(1)[0];if(1!==r.length)throw new o("\\gdef's first argument must be a macro name");var n=r[0].text,a=0;for(r=t.consumeArgs(1)[0];1===r.length&&"#"===r[0].text;){if(1!==(r=t.consumeArgs(1)[0]).length)throw new o('Invalid argument number length "'+r.length+'"');if(!/^[1-9]$/.test(r[0].text))throw new o('Invalid argument number "'+r[0].text+'"');if(a++,parseInt(r[0].text)!==a)throw new o('Argument number "'+r[0].text+'" out of order');r=t.consumeArgs(1)[0]}return t.macros.set(n,{tokens:r,numArgs:a},e),""};en("\\gdef",function(t){return nn(t,!0)}),en("\\def",function(t){return nn(t,!1)}),en("\\global",function(t){var e=t.consumeArgs(1)[0];if(1!==e.length)throw new o("Invalid command after \\global");var r=e[0].text;if("\\def"===r)return nn(t,!0);throw new o("Invalid command '"+r+"' after \\global")});var an=function(t,e,r){var n=t.consumeArgs(1)[0];if(1!==n.length)throw new o("\\newcommand's first argument must be a macro name");var a=n[0].text,i=t.isDefined(a);if(i&&!e)throw new o("\\newcommand{"+a+"} attempting to redefine "+a+"; use \\renewcommand");if(!i&&!r)throw new o("\\renewcommand{"+a+"} when command "+a+" does not yet exist; use \\newcommand");var s=0;if(1===(n=t.consumeArgs(1)[0]).length&&"["===n[0].text){for(var h="",l=t.expandNextToken();"]"!==l.text&&"EOF"!==l.text;)h+=l.text,l=t.expandNextToken();if(!h.match(/^\s*[0-9]+\s*$/))throw new o("Invalid number of arguments: "+h);s=parseInt(h),n=t.consumeArgs(1)[0]}return t.macros.set(a,{tokens:n,numArgs:s}),""};en("\\newcommand",function(t){return an(t,!1,!0)}),en("\\renewcommand",function(t){return an(t,!0,!1)}),en("\\providecommand",function(t){return an(t,!0,!0)}),en("\\bgroup","{"),en("\\egroup","}"),en("\\lq","`"),en("\\rq","'"),en("\\aa","\\r a"),en("\\AA","\\r A"),en("\\textcopyright","\\html@mathml{\\textcircled{c}}{\\char`\xa9}"),en("\\copyright","\\TextOrMath{\\textcopyright}{\\text{\\textcopyright}}"),en("\\textregistered","\\html@mathml{\\textcircled{\\scriptsize R}}{\\char`\xae}"),en("\u212c","\\mathscr{B}"),en("\u2130","\\mathscr{E}"),en("\u2131","\\mathscr{F}"),en("\u210b","\\mathscr{H}"),en("\u2110","\\mathscr{I}"),en("\u2112","\\mathscr{L}"),en("\u2133","\\mathscr{M}"),en("\u211b","\\mathscr{R}"),en("\u212d","\\mathfrak{C}"),en("\u210c","\\mathfrak{H}"),en("\u2128","\\mathfrak{Z}"),en("\xb7","\\cdotp"),en("\\llap","\\mathllap{\\textrm{#1}}"),en("\\rlap","\\mathrlap{\\textrm{#1}}"),en("\\clap","\\mathclap{\\textrm{#1}}"),en("\\not","\\mathrel{\\mathrlap\\@not}"),en("\\neq","\\html@mathml{\\mathrel{\\not=}}{\\mathrel{\\char`\u2260}}"),en("\\ne","\\neq"),en("\u2260","\\neq"),en("\\notin","\\html@mathml{\\mathrel{{\\in}\\mathllap{/\\mskip1mu}}}{\\mathrel{\\char`\u2209}}"),en("\u2209","\\notin"),en("\u2258","\\html@mathml{\\mathrel{=\\kern{-1em}\\raisebox{0.4em}{$\\scriptsize\\frown$}}}{\\mathrel{\\char`\u2258}}"),en("\u2259","\\html@mathml{\\stackrel{\\tiny\\wedge}{=}}{\\mathrel{\\char`\u2258}}"),en("\u225a","\\html@mathml{\\stackrel{\\tiny\\vee}{=}}{\\mathrel{\\char`\u225a}}"),en("\u225b","\\html@mathml{\\stackrel{\\scriptsize\\star}{=}}{\\mathrel{\\char`\u225b}}"),en("\u225d","\\html@mathml{\\stackrel{\\tiny\\mathrm{def}}{=}}{\\mathrel{\\char`\u225d}}"),en("\u225e","\\html@mathml{\\stackrel{\\tiny\\mathrm{m}}{=}}{\\mathrel{\\char`\u225e}}"),en("\u225f","\\html@mathml{\\stackrel{\\tiny?}{=}}{\\mathrel{\\char`\u225f}}"),en("\u27c2","\\perp"),en("\u203c","\\mathclose{!\\mkern-0.8mu!}"),en("\u220c","\\notni"),en("\u231c","\\ulcorner"),en("\u231d","\\urcorner"),en("\u231e","\\llcorner"),en("\u231f","\\lrcorner"),en("\xa9","\\copyright"),en("\xae","\\textregistered"),en("\ufe0f","\\textregistered"),en("\\vdots","\\mathord{\\varvdots\\rule{0pt}{15pt}}"),en("\u22ee","\\vdots"),en("\\varGamma","\\mathit{\\Gamma}"),en("\\varDelta","\\mathit{\\Delta}"),en("\\varTheta","\\mathit{\\Theta}"),en("\\varLambda","\\mathit{\\Lambda}"),en("\\varXi","\\mathit{\\Xi}"),en("\\varPi","\\mathit{\\Pi}"),en("\\varSigma","\\mathit{\\Sigma}"),en("\\varUpsilon","\\mathit{\\Upsilon}"),en("\\varPhi","\\mathit{\\Phi}"),en("\\varPsi","\\mathit{\\Psi}"),en("\\varOmega","\\mathit{\\Omega}"),en("\\colon","\\nobreak\\mskip2mu\\mathpunct{}\\mathchoice{\\mkern-3mu}{\\mkern-3mu}{}{}{:}\\mskip6mu"),en("\\boxed","\\fbox{$\\displaystyle{#1}$}"),en("\\iff","\\DOTSB\\;\\Longleftrightarrow\\;"),en("\\implies","\\DOTSB\\;\\Longrightarrow\\;"),en("\\impliedby","\\DOTSB\\;\\Longleftarrow\\;");var on={",":"\\dotsc","\\not":"\\dotsb","+":"\\dotsb","=":"\\dotsb","<":"\\dotsb",">":"\\dotsb","-":"\\dotsb","*":"\\dotsb",":":"\\dotsb","\\DOTSB":"\\dotsb","\\coprod":"\\dotsb","\\bigvee":"\\dotsb","\\bigwedge":"\\dotsb","\\biguplus":"\\dotsb","\\bigcap":"\\dotsb","\\bigcup":"\\dotsb","\\prod":"\\dotsb","\\sum":"\\dotsb","\\bigotimes":"\\dotsb","\\bigoplus":"\\dotsb","\\bigodot":"\\dotsb","\\bigsqcup":"\\dotsb","\\And":"\\dotsb","\\longrightarrow":"\\dotsb","\\Longrightarrow":"\\dotsb","\\longleftarrow":"\\dotsb","\\Longleftarrow":"\\dotsb","\\longleftrightarrow":"\\dotsb","\\Longleftrightarrow":"\\dotsb","\\mapsto":"\\dotsb","\\longmapsto":"\\dotsb","\\hookrightarrow":"\\dotsb","\\doteq":"\\dotsb","\\mathbin":"\\dotsb","\\mathrel":"\\dotsb","\\relbar":"\\dotsb","\\Relbar":"\\dotsb","\\xrightarrow":"\\dotsb","\\xleftarrow":"\\dotsb","\\DOTSI":"\\dotsi","\\int":"\\dotsi","\\oint":"\\dotsi","\\iint":"\\dotsi","\\iiint":"\\dotsi","\\iiiint":"\\dotsi","\\idotsint":"\\dotsi","\\DOTSX":"\\dotsx"};en("\\dots",function(t){var e="\\dotso",r=t.expandAfterFuture().text;return r in on?e=on[r]:"\\not"===r.substr(0,4)?e="\\dotsb":r in _.math&&c.contains(["bin","rel"],_.math[r].group)&&(e="\\dotsb"),e});var sn={")":!0,"]":!0,"\\rbrack":!0,"\\}":!0,"\\rbrace":!0,"\\rangle":!0,"\\rceil":!0,"\\rfloor":!0,"\\rgroup":!0,"\\rmoustache":!0,"\\right":!0,"\\bigr":!0,"\\biggr":!0,"\\Bigr":!0,"\\Biggr":!0,$:!0,";":!0,".":!0,",":!0};en("\\dotso",function(t){return t.future().text in sn?"\\ldots\\,":"\\ldots"}),en("\\dotsc",function(t){var e=t.future().text;return e in sn&&","!==e?"\\ldots\\,":"\\ldots"}),en("\\cdots",function(t){return t.future().text in sn?"\\@cdots\\,":"\\@cdots"}),en("\\dotsb","\\cdots"),en("\\dotsm","\\cdots"),en("\\dotsi","\\!\\cdots"),en("\\dotsx","\\ldots\\,"),en("\\DOTSI","\\relax"),en("\\DOTSB","\\relax"),en("\\DOTSX","\\relax"),en("\\tmspace","\\TextOrMath{\\kern#1#3}{\\mskip#1#2}\\relax"),en("\\,","\\tmspace+{3mu}{.1667em}"),en("\\thinspace","\\,"),en("\\>","\\mskip{4mu}"),en("\\:","\\tmspace+{4mu}{.2222em}"),en("\\medspace","\\:"),en("\\;","\\tmspace+{5mu}{.2777em}"),en("\\thickspace","\\;"),en("\\!","\\tmspace-{3mu}{.1667em}"),en("\\negthinspace","\\!"),en("\\negmedspace","\\tmspace-{4mu}{.2222em}"),en("\\negthickspace","\\tmspace-{5mu}{.277em}"),en("\\enspace","\\kern.5em "),en("\\enskip","\\hskip.5em\\relax"),en("\\quad","\\hskip1em\\relax"),en("\\qquad","\\hskip2em\\relax"),en("\\tag","\\@ifstar\\tag@literal\\tag@paren"),en("\\tag@paren","\\tag@literal{({#1})}"),en("\\tag@literal",function(t){if(t.macros.get("\\df@tag"))throw new o("Multiple \\tag");return"\\gdef\\df@tag{\\text{#1}}"}),en("\\bmod","\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}\\mathbin{\\rm mod}\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}"),en("\\pod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern8mu}{\\mkern8mu}{\\mkern8mu}(#1)"),en("\\pmod","\\pod{{\\rm mod}\\mkern6mu#1}"),en("\\mod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern12mu}{\\mkern12mu}{\\mkern12mu}{\\rm mod}\\,\\,#1"),en("\\pmb","\\html@mathml{\\@binrel{#1}{\\mathrlap{#1}\\mathrlap{\\mkern0.4mu\\raisebox{0.4mu}{$#1$}}{\\mkern0.8mu#1}}}{\\mathbf{#1}}"),en("\\\\","\\newline"),en("\\TeX","\\textrm{\\html@mathml{T\\kern-.1667em\\raisebox{-.5ex}{E}\\kern-.125emX}{TeX}}");var hn=H["Main-Regular"]["T".charCodeAt(0)][1]-.7*H["Main-Regular"]["A".charCodeAt(0)][1]+"em";en("\\LaTeX","\\textrm{\\html@mathml{L\\kern-.36em\\raisebox{"+hn+"}{\\scriptsize A}\\kern-.15em\\TeX}{LaTeX}}"),en("\\KaTeX","\\textrm{\\html@mathml{K\\kern-.17em\\raisebox{"+hn+"}{\\scriptsize A}\\kern-.15em\\TeX}{KaTeX}}"),en("\\hspace","\\@ifstar\\@hspacer\\@hspace"),en("\\@hspace","\\hskip #1\\relax"),en("\\@hspacer","\\rule{0pt}{0pt}\\hskip #1\\relax"),en("\\ordinarycolon",":"),en("\\vcentcolon","\\mathrel{\\mathop\\ordinarycolon}"),en("\\dblcolon","\\mathrel{\\vcentcolon\\mathrel{\\mkern-.9mu}\\vcentcolon}"),en("\\coloneqq","\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}=}"),en("\\Coloneqq","\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}=}"),en("\\coloneq","\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}"),en("\\Coloneq","\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}"),en("\\eqqcolon","\\mathrel{=\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),en("\\Eqqcolon","\\mathrel{=\\mathrel{\\mkern-1.2mu}\\dblcolon}"),en("\\eqcolon","\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),en("\\Eqcolon","\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\dblcolon}"),en("\\colonapprox","\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\approx}"),en("\\Colonapprox","\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\approx}"),en("\\colonsim","\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\sim}"),en("\\Colonsim","\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\sim}"),en("\u2254","\\coloneqq"),en("\u2255","\\eqqcolon"),en("\u2a74","\\Coloneqq"),en("\\ratio","\\vcentcolon"),en("\\coloncolon","\\dblcolon"),en("\\colonequals","\\coloneqq"),en("\\coloncolonequals","\\Coloneqq"),en("\\equalscolon","\\eqqcolon"),en("\\equalscoloncolon","\\Eqqcolon"),en("\\colonminus","\\coloneq"),en("\\coloncolonminus","\\Coloneq"),en("\\minuscolon","\\eqcolon"),en("\\minuscoloncolon","\\Eqcolon"),en("\\coloncolonapprox","\\Colonapprox"),en("\\coloncolonsim","\\Colonsim"),en("\\simcolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),en("\\simcoloncolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\dblcolon}"),en("\\approxcolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),en("\\approxcoloncolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\dblcolon}"),en("\\notni","\\html@mathml{\\not\\ni}{\\mathrel{\\char`\u220c}}"),en("\\limsup","\\DOTSB\\mathop{\\operatorname{lim\\,sup}}\\limits"),en("\\liminf","\\DOTSB\\mathop{\\operatorname{lim\\,inf}}\\limits"),en("\u27e6","\\mathopen{[\\mkern-3.2mu[}"),en("\u27e7","\\mathclose{]\\mkern-3.2mu]}"),en("\\darr","\\downarrow"),en("\\dArr","\\Downarrow"),en("\\Darr","\\Downarrow"),en("\\lang","\\langle"),en("\\rang","\\rangle"),en("\\uarr","\\uparrow"),en("\\uArr","\\Uparrow"),en("\\Uarr","\\Uparrow"),en("\\N","\\mathbb{N}"),en("\\R","\\mathbb{R}"),en("\\Z","\\mathbb{Z}"),en("\\alef","\\aleph"),en("\\alefsym","\\aleph"),en("\\Alpha","\\mathrm{A}"),en("\\Beta","\\mathrm{B}"),en("\\bull","\\bullet"),en("\\Chi","\\mathrm{X}"),en("\\clubs","\\clubsuit"),en("\\cnums","\\mathbb{C}"),en("\\Complex","\\mathbb{C}"),en("\\Dagger","\\ddagger"),en("\\diamonds","\\diamondsuit"),en("\\empty","\\emptyset"),en("\\Epsilon","\\mathrm{E}"),en("\\Eta","\\mathrm{H}"),en("\\exist","\\exists"),en("\\harr","\\leftrightarrow"),en("\\hArr","\\Leftrightarrow"),en("\\Harr","\\Leftrightarrow"),en("\\hearts","\\heartsuit"),en("\\image","\\Im"),en("\\infin","\\infty"),en("\\Iota","\\mathrm{I}"),en("\\isin","\\in"),en("\\Kappa","\\mathrm{K}"),en("\\larr","\\leftarrow"),en("\\lArr","\\Leftarrow"),en("\\Larr","\\Leftarrow"),en("\\lrarr","\\leftrightarrow"),en("\\lrArr","\\Leftrightarrow"),en("\\Lrarr","\\Leftrightarrow"),en("\\Mu","\\mathrm{M}"),en("\\natnums","\\mathbb{N}"),en("\\Nu","\\mathrm{N}"),en("\\Omicron","\\mathrm{O}"),en("\\plusmn","\\pm"),en("\\rarr","\\rightarrow"),en("\\rArr","\\Rightarrow"),en("\\Rarr","\\Rightarrow"),en("\\real","\\Re"),en("\\reals","\\mathbb{R}"),en("\\Reals","\\mathbb{R}"),en("\\Rho","\\mathrm{R}"),en("\\sdot","\\cdot"),en("\\sect","\\S"),en("\\spades","\\spadesuit"),en("\\sub","\\subset"),en("\\sube","\\subseteq"),en("\\supe","\\supseteq"),en("\\Tau","\\mathrm{T}"),en("\\thetasym","\\vartheta"),en("\\weierp","\\wp"),en("\\Zeta","\\mathrm{Z}"),en("\\argmin","\\DOTSB\\mathop{\\operatorname{arg\\,min}}\\limits"),en("\\argmax","\\DOTSB\\mathop{\\operatorname{arg\\,max}}\\limits");var ln={"\\relax":!0,"^":!0,_:!0,"\\limits":!0,"\\nolimits":!0},mn=function(){function t(t,e,r){this.settings=void 0,this.expansionCount=void 0,this.lexer=void 0,this.macros=void 0,this.stack=void 0,this.mode=void 0,this.settings=e,this.expansionCount=0,this.feed(t),this.macros=new Jr(tn,e.macros),this.mode=r,this.stack=[]}var e=t.prototype;return e.feed=function(t){this.lexer=new Kr(t,this.settings)},e.switchMode=function(t){this.mode=t},e.beginGroup=function(){this.macros.beginGroup()},e.endGroup=function(){this.macros.endGroup()},e.future=function(){return 0===this.stack.length&&this.pushToken(this.lexer.lex()),this.stack[this.stack.length-1]},e.popToken=function(){return this.future(),this.stack.pop()},e.pushToken=function(t){this.stack.push(t)},e.pushTokens=function(t){var e;(e=this.stack).push.apply(e,t)},e.consumeSpaces=function(){for(;;){if(" "!==this.future().text)break;this.stack.pop()}},e.consumeArgs=function(t){for(var e=[],r=0;r<t;++r){this.consumeSpaces();var n=this.popToken();if("{"===n.text){for(var a=[],i=1;0!==i;){var s=this.popToken();if(a.push(s),"{"===s.text)++i;else if("}"===s.text)--i;else if("EOF"===s.text)throw new o("End of input in macro argument",n)}a.pop(),a.reverse(),e[r]=a}else{if("EOF"===n.text)throw new o("End of input expecting macro argument");e[r]=[n]}}return e},e.expandOnce=function(){var t=this.popToken(),e=t.text,r=this._getExpansion(e);if(null==r)return this.pushToken(t),t;if(this.expansionCount++,this.expansionCount>this.settings.maxExpand)throw new o("Too many expansions: infinite loop or need to increase maxExpand setting");var n=r.tokens;if(r.numArgs)for(var a=this.consumeArgs(r.numArgs),i=(n=n.slice()).length-1;i>=0;--i){var s=n[i];if("#"===s.text){if(0===i)throw new o("Incomplete placeholder at end of macro body",s);if("#"===(s=n[--i]).text)n.splice(i+1,1);else{if(!/^[1-9]$/.test(s.text))throw new o("Not a valid argument number",s);var h;(h=n).splice.apply(h,[i,2].concat(a[+s.text-1]))}}}return this.pushTokens(n),n},e.expandAfterFuture=function(){return this.expandOnce(),this.future()},e.expandNextToken=function(){for(;;){var t=this.expandOnce();if(t instanceof a){if("\\relax"!==t.text)return this.stack.pop();this.stack.pop()}}throw new Error},e.expandMacro=function(t){if(this.macros.get(t)){var e=[],r=this.stack.length;for(this.pushToken(new a(t));this.stack.length>r;){this.expandOnce()instanceof a&&e.push(this.stack.pop())}return e}},e.expandMacroAsText=function(t){var e=this.expandMacro(t);return e?e.map(function(t){return t.text}).join(""):e},e._getExpansion=function(t){var e=this.macros.get(t);if(null==e)return e;var r="function"==typeof e?e(this):e;if("string"==typeof r){var n=0;if(-1!==r.indexOf("#"))for(var a=r.replace(/##/g,"");-1!==a.indexOf("#"+(n+1));)++n;for(var i=new Kr(r,this.settings),o=[],s=i.lex();"EOF"!==s.text;)o.push(s),s=i.lex();return o.reverse(),{tokens:o,numArgs:n}}return r},e.isDefined=function(t){return this.macros.has(t)||Wr.hasOwnProperty(t)||_.math.hasOwnProperty(t)||_.text.hasOwnProperty(t)||ln.hasOwnProperty(t)},t}(),cn={"\u0301":{text:"\\'",math:"\\acute"},"\u0300":{text:"\\`",math:"\\grave"},"\u0308":{text:'\\"',math:"\\ddot"},"\u0303":{text:"\\~",math:"\\tilde"},"\u0304":{text:"\\=",math:"\\bar"},"\u0306":{text:"\\u",math:"\\breve"},"\u030c":{text:"\\v",math:"\\check"},"\u0302":{text:"\\^",math:"\\hat"},"\u0307":{text:"\\.",math:"\\dot"},"\u030a":{text:"\\r",math:"\\mathring"},"\u030b":{text:"\\H"}},un={"\xe1":"a\u0301","\xe0":"a\u0300","\xe4":"a\u0308","\u01df":"a\u0308\u0304","\xe3":"a\u0303","\u0101":"a\u0304","\u0103":"a\u0306","\u1eaf":"a\u0306\u0301","\u1eb1":"a\u0306\u0300","\u1eb5":"a\u0306\u0303","\u01ce":"a\u030c","\xe2":"a\u0302","\u1ea5":"a\u0302\u0301","\u1ea7":"a\u0302\u0300","\u1eab":"a\u0302\u0303","\u0227":"a\u0307","\u01e1":"a\u0307\u0304","\xe5":"a\u030a","\u01fb":"a\u030a\u0301","\u1e03":"b\u0307","\u0107":"c\u0301","\u010d":"c\u030c","\u0109":"c\u0302","\u010b":"c\u0307","\u010f":"d\u030c","\u1e0b":"d\u0307","\xe9":"e\u0301","\xe8":"e\u0300","\xeb":"e\u0308","\u1ebd":"e\u0303","\u0113":"e\u0304","\u1e17":"e\u0304\u0301","\u1e15":"e\u0304\u0300","\u0115":"e\u0306","\u011b":"e\u030c","\xea":"e\u0302","\u1ebf":"e\u0302\u0301","\u1ec1":"e\u0302\u0300","\u1ec5":"e\u0302\u0303","\u0117":"e\u0307","\u1e1f":"f\u0307","\u01f5":"g\u0301","\u1e21":"g\u0304","\u011f":"g\u0306","\u01e7":"g\u030c","\u011d":"g\u0302","\u0121":"g\u0307","\u1e27":"h\u0308","\u021f":"h\u030c","\u0125":"h\u0302","\u1e23":"h\u0307","\xed":"i\u0301","\xec":"i\u0300","\xef":"i\u0308","\u1e2f":"i\u0308\u0301","\u0129":"i\u0303","\u012b":"i\u0304","\u012d":"i\u0306","\u01d0":"i\u030c","\xee":"i\u0302","\u01f0":"j\u030c","\u0135":"j\u0302","\u1e31":"k\u0301","\u01e9":"k\u030c","\u013a":"l\u0301","\u013e":"l\u030c","\u1e3f":"m\u0301","\u1e41":"m\u0307","\u0144":"n\u0301","\u01f9":"n\u0300","\xf1":"n\u0303","\u0148":"n\u030c","\u1e45":"n\u0307","\xf3":"o\u0301","\xf2":"o\u0300","\xf6":"o\u0308","\u022b":"o\u0308\u0304","\xf5":"o\u0303","\u1e4d":"o\u0303\u0301","\u1e4f":"o\u0303\u0308","\u022d":"o\u0303\u0304","\u014d":"o\u0304","\u1e53":"o\u0304\u0301","\u1e51":"o\u0304\u0300","\u014f":"o\u0306","\u01d2":"o\u030c","\xf4":"o\u0302","\u1ed1":"o\u0302\u0301","\u1ed3":"o\u0302\u0300","\u1ed7":"o\u0302\u0303","\u022f":"o\u0307","\u0231":"o\u0307\u0304","\u0151":"o\u030b","\u1e55":"p\u0301","\u1e57":"p\u0307","\u0155":"r\u0301","\u0159":"r\u030c","\u1e59":"r\u0307","\u015b":"s\u0301","\u1e65":"s\u0301\u0307","\u0161":"s\u030c","\u1e67":"s\u030c\u0307","\u015d":"s\u0302","\u1e61":"s\u0307","\u1e97":"t\u0308","\u0165":"t\u030c","\u1e6b":"t\u0307","\xfa":"u\u0301","\xf9":"u\u0300","\xfc":"u\u0308","\u01d8":"u\u0308\u0301","\u01dc":"u\u0308\u0300","\u01d6":"u\u0308\u0304","\u01da":"u\u0308\u030c","\u0169":"u\u0303","\u1e79":"u\u0303\u0301","\u016b":"u\u0304","\u1e7b":"u\u0304\u0308","\u016d":"u\u0306","\u01d4":"u\u030c","\xfb":"u\u0302","\u016f":"u\u030a","\u0171":"u\u030b","\u1e7d":"v\u0303","\u1e83":"w\u0301","\u1e81":"w\u0300","\u1e85":"w\u0308","\u0175":"w\u0302","\u1e87":"w\u0307","\u1e98":"w\u030a","\u1e8d":"x\u0308","\u1e8b":"x\u0307","\xfd":"y\u0301","\u1ef3":"y\u0300","\xff":"y\u0308","\u1ef9":"y\u0303","\u0233":"y\u0304","\u0177":"y\u0302","\u1e8f":"y\u0307","\u1e99":"y\u030a","\u017a":"z\u0301","\u017e":"z\u030c","\u1e91":"z\u0302","\u017c":"z\u0307","\xc1":"A\u0301","\xc0":"A\u0300","\xc4":"A\u0308","\u01de":"A\u0308\u0304","\xc3":"A\u0303","\u0100":"A\u0304","\u0102":"A\u0306","\u1eae":"A\u0306\u0301","\u1eb0":"A\u0306\u0300","\u1eb4":"A\u0306\u0303","\u01cd":"A\u030c","\xc2":"A\u0302","\u1ea4":"A\u0302\u0301","\u1ea6":"A\u0302\u0300","\u1eaa":"A\u0302\u0303","\u0226":"A\u0307","\u01e0":"A\u0307\u0304","\xc5":"A\u030a","\u01fa":"A\u030a\u0301","\u1e02":"B\u0307","\u0106":"C\u0301","\u010c":"C\u030c","\u0108":"C\u0302","\u010a":"C\u0307","\u010e":"D\u030c","\u1e0a":"D\u0307","\xc9":"E\u0301","\xc8":"E\u0300","\xcb":"E\u0308","\u1ebc":"E\u0303","\u0112":"E\u0304","\u1e16":"E\u0304\u0301","\u1e14":"E\u0304\u0300","\u0114":"E\u0306","\u011a":"E\u030c","\xca":"E\u0302","\u1ebe":"E\u0302\u0301","\u1ec0":"E\u0302\u0300","\u1ec4":"E\u0302\u0303","\u0116":"E\u0307","\u1e1e":"F\u0307","\u01f4":"G\u0301","\u1e20":"G\u0304","\u011e":"G\u0306","\u01e6":"G\u030c","\u011c":"G\u0302","\u0120":"G\u0307","\u1e26":"H\u0308","\u021e":"H\u030c","\u0124":"H\u0302","\u1e22":"H\u0307","\xcd":"I\u0301","\xcc":"I\u0300","\xcf":"I\u0308","\u1e2e":"I\u0308\u0301","\u0128":"I\u0303","\u012a":"I\u0304","\u012c":"I\u0306","\u01cf":"I\u030c","\xce":"I\u0302","\u0130":"I\u0307","\u0134":"J\u0302","\u1e30":"K\u0301","\u01e8":"K\u030c","\u0139":"L\u0301","\u013d":"L\u030c","\u1e3e":"M\u0301","\u1e40":"M\u0307","\u0143":"N\u0301","\u01f8":"N\u0300","\xd1":"N\u0303","\u0147":"N\u030c","\u1e44":"N\u0307","\xd3":"O\u0301","\xd2":"O\u0300","\xd6":"O\u0308","\u022a":"O\u0308\u0304","\xd5":"O\u0303","\u1e4c":"O\u0303\u0301","\u1e4e":"O\u0303\u0308","\u022c":"O\u0303\u0304","\u014c":"O\u0304","\u1e52":"O\u0304\u0301","\u1e50":"O\u0304\u0300","\u014e":"O\u0306","\u01d1":"O\u030c","\xd4":"O\u0302","\u1ed0":"O\u0302\u0301","\u1ed2":"O\u0302\u0300","\u1ed6":"O\u0302\u0303","\u022e":"O\u0307","\u0230":"O\u0307\u0304","\u0150":"O\u030b","\u1e54":"P\u0301","\u1e56":"P\u0307","\u0154":"R\u0301","\u0158":"R\u030c","\u1e58":"R\u0307","\u015a":"S\u0301","\u1e64":"S\u0301\u0307","\u0160":"S\u030c","\u1e66":"S\u030c\u0307","\u015c":"S\u0302","\u1e60":"S\u0307","\u0164":"T\u030c","\u1e6a":"T\u0307","\xda":"U\u0301","\xd9":"U\u0300","\xdc":"U\u0308","\u01d7":"U\u0308\u0301","\u01db":"U\u0308\u0300","\u01d5":"U\u0308\u0304","\u01d9":"U\u0308\u030c","\u0168":"U\u0303","\u1e78":"U\u0303\u0301","\u016a":"U\u0304","\u1e7a":"U\u0304\u0308","\u016c":"U\u0306","\u01d3":"U\u030c","\xdb":"U\u0302","\u016e":"U\u030a","\u0170":"U\u030b","\u1e7c":"V\u0303","\u1e82":"W\u0301","\u1e80":"W\u0300","\u1e84":"W\u0308","\u0174":"W\u0302","\u1e86":"W\u0307","\u1e8c":"X\u0308","\u1e8a":"X\u0307","\xdd":"Y\u0301","\u1ef2":"Y\u0300","\u0178":"Y\u0308","\u1ef8":"Y\u0303","\u0232":"Y\u0304","\u0176":"Y\u0302","\u1e8e":"Y\u0307","\u0179":"Z\u0301","\u017d":"Z\u030c","\u1e90":"Z\u0302","\u017b":"Z\u0307","\u03ac":"\u03b1\u0301","\u1f70":"\u03b1\u0300","\u1fb1":"\u03b1\u0304","\u1fb0":"\u03b1\u0306","\u03ad":"\u03b5\u0301","\u1f72":"\u03b5\u0300","\u03ae":"\u03b7\u0301","\u1f74":"\u03b7\u0300","\u03af":"\u03b9\u0301","\u1f76":"\u03b9\u0300","\u03ca":"\u03b9\u0308","\u0390":"\u03b9\u0308\u0301","\u1fd2":"\u03b9\u0308\u0300","\u1fd1":"\u03b9\u0304","\u1fd0":"\u03b9\u0306","\u03cc":"\u03bf\u0301","\u1f78":"\u03bf\u0300","\u03cd":"\u03c5\u0301","\u1f7a":"\u03c5\u0300","\u03cb":"\u03c5\u0308","\u03b0":"\u03c5\u0308\u0301","\u1fe2":"\u03c5\u0308\u0300","\u1fe1":"\u03c5\u0304","\u1fe0":"\u03c5\u0306","\u03ce":"\u03c9\u0301","\u1f7c":"\u03c9\u0300","\u038e":"\u03a5\u0301","\u1fea":"\u03a5\u0300","\u03ab":"\u03a5\u0308","\u1fe9":"\u03a5\u0304","\u1fe8":"\u03a5\u0306","\u038f":"\u03a9\u0301","\u1ffa":"\u03a9\u0300"},dn=function(){function t(t,e){this.mode=void 0,this.gullet=void 0,this.settings=void 0,this.leftrightDepth=void 0,this.nextToken=void 0,this.mode="math",this.gullet=new mn(t,e,this.mode),this.settings=e,this.leftrightDepth=0}var e=t.prototype;return e.expect=function(t,e){if(void 0===e&&(e=!0),this.nextToken.text!==t)throw new o("Expected '"+t+"', got '"+this.nextToken.text+"'",this.nextToken);e&&this.consume()},e.consume=function(){this.nextToken=this.gullet.expandNextToken()},e.switchMode=function(t){this.mode=t,this.gullet.switchMode(t)},e.parse=function(){this.gullet.beginGroup(),this.settings.colorIsTextColor&&this.gullet.macros.set("\\color","\\textcolor"),this.consume();var t=this.parseExpression(!1);return this.expect("EOF",!1),this.gullet.endGroup(),t},e.parseExpression=function(e,r){for(var n=[];;){"math"===this.mode&&this.consumeSpaces();var a=this.nextToken;if(-1!==t.endOfExpression.indexOf(a.text))break;if(r&&a.text===r)break;if(e&&Wr[a.text]&&Wr[a.text].infix)break;var i=this.parseAtom(r);if(!i)break;n.push(i)}return"text"===this.mode&&this.formLigatures(n),this.handleInfixNodes(n)},e.handleInfixNodes=function(t){for(var e,r=-1,n=0;n<t.length;n++){var a=Pt(t[n],"infix");if(a){if(-1!==r)throw new o("only one infix operator per group",a.token);r=n,e=a.replaceWith}}if(-1!==r&&e){var i,s,h=t.slice(0,r),l=t.slice(r+1);return i=1===h.length&&"ordgroup"===h[0].type?h[0]:{type:"ordgroup",mode:this.mode,body:h},s=1===l.length&&"ordgroup"===l[0].type?l[0]:{type:"ordgroup",mode:this.mode,body:l},["\\\\abovefrac"===e?this.callFunction(e,[i,t[r],s],[]):this.callFunction(e,[i,s],[])]}return t},e.handleSupSubscript=function(e){var r=this.nextToken,n=r.text;this.consume(),this.consumeSpaces();var a=this.parseGroup(e,!1,t.SUPSUB_GREEDINESS);if(!a)throw new o("Expected group after '"+n+"'",r);return a},e.handleUnsupportedCmd=function(){for(var t=this.nextToken.text,e=[],r=0;r<t.length;r++)e.push({type:"textord",mode:"text",text:t[r]});var n={type:"text",mode:this.mode,body:e},a={type:"color",mode:this.mode,color:this.settings.errorColor,body:[n]};return this.consume(),a},e.parseAtom=function(t){var e,r,n=this.parseGroup("atom",!1,null,t);if("text"===this.mode)return n;for(;;){this.consumeSpaces();var a=this.nextToken;if("\\limits"===a.text||"\\nolimits"===a.text){var i=Pt(n,"op");if(!i)throw new o("Limit controls must follow a math operator",a);var s="\\limits"===a.text;i.limits=s,i.alwaysHandleSupSub=!0,this.consume()}else if("^"===a.text){if(e)throw new o("Double superscript",a);e=this.handleSupSubscript("superscript")}else if("_"===a.text){if(r)throw new o("Double subscript",a);r=this.handleSupSubscript("subscript")}else{if("'"!==a.text)break;if(e)throw new o("Double superscript",a);var h={type:"textord",mode:this.mode,text:"\\prime"},l=[h];for(this.consume();"'"===this.nextToken.text;)l.push(h),this.consume();"^"===this.nextToken.text&&l.push(this.handleSupSubscript("superscript")),e={type:"ordgroup",mode:this.mode,body:l}}}return e||r?{type:"supsub",mode:this.mode,base:n,sup:e,sub:r}:n},e.parseFunction=function(t,e,r){var n=this.nextToken,a=n.text,i=Wr[a];if(!i)return null;if(null!=r&&i.greediness<=r)throw new o("Got function '"+a+"' with no arguments"+(e?" as "+e:""),n);if("text"===this.mode&&!i.allowedInText)throw new o("Can't use function '"+a+"' in text mode",n);if("math"===this.mode&&!1===i.allowedInMath)throw new o("Can't use function '"+a+"' in math mode",n);if(i.argTypes&&"url"===i.argTypes[0]&&this.gullet.lexer.setCatcode("%",13),i.consumeMode){var s=this.mode;this.switchMode(i.consumeMode),this.consume(),this.switchMode(s)}else this.consume();var h=this.parseArguments(a,i),l=h.args,m=h.optArgs;return this.callFunction(a,l,m,n,t)},e.callFunction=function(t,e,r,n,a){var i={funcName:t,parser:this,token:n,breakOnTokenText:a},s=Wr[t];if(s&&s.handler)return s.handler(i,e,r);throw new o("No function handler for "+t)},e.parseArguments=function(t,e){var r=e.numArgs+e.numOptionalArgs;if(0===r)return{args:[],optArgs:[]};for(var n=e.greediness,a=[],i=[],s=0;s<r;s++){var h=e.argTypes&&e.argTypes[s],l=s<e.numOptionalArgs;s>0&&!l&&this.consumeSpaces(),0!==s||l||"math"!==this.mode||this.consumeSpaces();var m=this.nextToken,c=this.parseGroupOfType("argument to '"+t+"'",h,l,n);if(!c){if(l){i.push(null);continue}throw new o("Expected group after '"+t+"'",m)}(l?i:a).push(c)}return{args:a,optArgs:i}},e.parseGroupOfType=function(t,e,r,n){switch(e){case"color":return this.parseColorGroup(r);case"size":return this.parseSizeGroup(r);case"url":return this.parseUrlGroup(r);case"math":case"text":return this.parseGroup(t,r,n,void 0,e);case"raw":if(r&&"{"===this.nextToken.text)return null;var a=this.parseStringGroup("raw",r,!0);if(a)return{type:"raw",mode:"text",string:a.text};throw new o("Expected raw group",this.nextToken);case"original":case null:case void 0:return this.parseGroup(t,r,n);default:throw new o("Unknown group type as "+t,this.nextToken)}},e.consumeSpaces=function(){for(;" "===this.nextToken.text;)this.consume()},e.parseStringGroup=function(t,e,r){var n=e?"[":"{",a=e?"]":"}",i=this.nextToken;if(i.text!==n){if(e)return null;if(r&&"EOF"!==i.text&&/[^{}[\]]/.test(i.text))return this.gullet.lexer.setCatcode("%",14),this.consume(),i}var s=this.mode;this.mode="text",this.expect(n);for(var h="",l=this.nextToken,m=0,c=l;r&&m>0||this.nextToken.text!==a;){switch(this.nextToken.text){case"EOF":throw new o("Unexpected end of input in "+t,l.range(c,h));case n:m++;break;case a:m--}h+=(c=this.nextToken).text,this.consume()}return this.mode=s,this.gullet.lexer.setCatcode("%",14),this.expect(a),l.range(c,h)},e.parseRegexGroup=function(t,e){var r=this.mode;this.mode="text";for(var n=this.nextToken,a=n,i="";"EOF"!==this.nextToken.text&&t.test(i+this.nextToken.text);)i+=(a=this.nextToken).text,this.consume();if(""===i)throw new o("Invalid "+e+": '"+n.text+"'",n);return this.mode=r,n.range(a,i)},e.parseColorGroup=function(t){var e=this.parseStringGroup("color",t);if(!e)return null;var r=/^(#[a-f0-9]{3}|#?[a-f0-9]{6}|[a-z]+)$/i.exec(e.text);if(!r)throw new o("Invalid color: '"+e.text+"'",e);var n=r[0];return/^[0-9a-f]{6}$/i.test(n)&&(n="#"+n),{type:"color-token",mode:this.mode,color:n}},e.parseSizeGroup=function(t){var e,r=!1;if(!(e=t||"{"===this.nextToken.text?this.parseStringGroup("size",t):this.parseRegexGroup(/^[-+]? *(?:$|\d+|\d+\.\d*|\.\d*) *[a-z]{0,2} *$/,"size")))return null;t||0!==e.text.length||(e.text="0pt",r=!0);var n=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(e.text);if(!n)throw new o("Invalid size: '"+e.text+"'",e);var a,i={number:+(n[1]+n[2]),unit:n[3]};if("string"!=typeof(a=i)&&(a=a.unit),!(a in St||a in zt||"ex"===a))throw new o("Invalid unit: '"+i.unit+"'",e);return{type:"size",mode:this.mode,value:i,isBlank:r}},e.parseUrlGroup=function(t){var e=this.parseStringGroup("url",t,!0);if(!e)return null;var r=e.text.replace(/\\([#$%&~_^{}])/g,"$1"),n=/^\s*([^\\\/#]*?)(?::|&#0*58|&#x0*3a)/i.exec(r);n=null!=n?n[1]:"_relative";var a=this.settings.allowedProtocols;if(!c.contains(a,"*")&&!c.contains(a,n))throw new o("Forbidden protocol '"+n+"'",e);return{type:"url",mode:this.mode,url:r}},e.parseGroup=function(e,r,a,i,s){var h,l,m=this.mode,c=this.nextToken,u=c.text;if(s&&this.switchMode(s),r?"["===u:"{"===u||"\\begingroup"===u){h=t.endOfGroup[u],this.gullet.beginGroup(),this.consume();var d=this.parseExpression(!1,h),p=this.nextToken;this.gullet.endGroup(),l={type:"ordgroup",mode:this.mode,loc:n.range(c,p),body:d,semisimple:"\\begingroup"===u||void 0}}else if(r)l=null;else if(null==(l=this.parseFunction(i,e,a)||this.parseSymbol())&&"\\"===u[0]&&!ln.hasOwnProperty(u)){if(this.settings.throwOnError)throw new o("Undefined control sequence: "+u,c);l=this.handleUnsupportedCmd()}return s&&this.switchMode(m),h&&this.expect(h),l},e.formLigatures=function(t){for(var e=t.length-1,r=0;r<e;++r){var a=t[r],i=a.text;"-"===i&&"-"===t[r+1].text&&(r+1<e&&"-"===t[r+2].text?(t.splice(r,3,{type:"textord",mode:"text",loc:n.range(a,t[r+2]),text:"---"}),e-=2):(t.splice(r,2,{type:"textord",mode:"text",loc:n.range(a,t[r+1]),text:"--"}),e-=1)),"'"!==i&&"`"!==i||t[r+1].text!==i||(t.splice(r,2,{type:"textord",mode:"text",loc:n.range(a,t[r+1]),text:i+i}),e-=1)}},e.parseSymbol=function(){var t=this.nextToken,e=t.text;if(/^\\verb[^a-zA-Z]/.test(e)){this.consume();var r=e.slice(5),a="*"===r.charAt(0);if(a&&(r=r.slice(1)),r.length<2||r.charAt(0)!==r.slice(-1))throw new o("\\verb assertion failed --\n                    please report what input caused this bug");return{type:"verb",mode:"text",body:r=r.slice(1,-1),star:a}}un.hasOwnProperty(e[0])&&!_[this.mode][e[0]]&&(this.settings.strict&&"math"===this.mode&&this.settings.reportNonstrict("unicodeTextInMathMode",'Accented Unicode text character "'+e[0]+'" used in math mode',t),e=un[e[0]]+e.substr(1));var i,s=$r.exec(e);if(s&&("i"===(e=e.substring(0,s.index))?e="\u0131":"j"===e&&(e="\u0237")),_[this.mode][e]){this.settings.strict&&"math"===this.mode&&"\xc7\xd0\xde\xe7\xfe".indexOf(e)>=0&&this.settings.reportNonstrict("unicodeTextInMathMode",'Latin-1/Unicode text character "'+e[0]+'" used in math mode',t);var h,l=_[this.mode][e].group,m=n.range(t);if(G.hasOwnProperty(l)){var c=l;h={type:"atom",mode:this.mode,family:c,loc:m,text:e}}else h={type:l,mode:this.mode,loc:m,text:e};i=h}else{if(!(e.charCodeAt(0)>=128))return null;this.settings.strict&&(z(e.charCodeAt(0))?"math"===this.mode&&this.settings.reportNonstrict("unicodeTextInMathMode",'Unicode text character "'+e[0]+'" used in math mode',t):this.settings.reportNonstrict("unknownSymbol",'Unrecognized Unicode character "'+e[0]+'" ('+e.charCodeAt(0)+")",t)),i={type:"textord",mode:this.mode,loc:n.range(t),text:e}}if(this.consume(),s)for(var u=0;u<s[0].length;u++){var d=s[0][u];if(!cn[d])throw new o("Unknown accent ' "+d+"'",t);var p=cn[d][this.mode];if(!p)throw new o("Accent "+d+" unsupported in "+this.mode+" mode",t);i={type:"accent",mode:this.mode,loc:n.range(t),label:p,isStretchy:!1,isShifty:!0,base:i}}return i},t}();dn.endOfExpression=["}","\\endgroup","\\end","\\right","&"],dn.endOfGroup={"[":"]","{":"}","\\begingroup":"\\endgroup"},dn.SUPSUB_GREEDINESS=1;var pn=function(t,e){if(!("string"==typeof t||t instanceof String))throw new TypeError("KaTeX can only parse string typed expression");var r=new dn(t,e);delete r.gullet.macros.current["\\df@tag"];var n=r.parse();if(r.gullet.macros.get("\\df@tag")){if(!e.displayMode)throw new o("\\tag works only in display equations");r.gullet.feed("\\df@tag"),n=[{type:"tag",mode:"text",body:n,tag:r.parse()}]}return n},fn=function(t,e,r){e.textContent="";var n=xn(t,r).toNode();e.appendChild(n)};"undefined"!=typeof document&&"CSS1Compat"!==document.compatMode&&("undefined"!=typeof console&&console.warn("Warning: KaTeX doesn't work in quirks mode. Make sure your website has a suitable doctype."),fn=function(){throw new o("KaTeX doesn't work in quirks mode.")});var gn=function(t,e,r){if(r.throwOnError||!(t instanceof o))throw t;var n=Dt.makeSpan(["katex-error"],[new I(e)]);return n.setAttribute("title",t.toString()),n.setAttribute("style","color:"+r.errorColor),n},xn=function(t,e){var r=new u(e);try{var n=pn(t,r);return ze(n,t,r)}catch(e){return gn(e,t,r)}},vn={version:"0.10.1",render:fn,renderToString:function(t,e){return xn(t,e).toMarkup()},ParseError:o,__parse:function(t,e){var r=new u(e);return pn(t,r)},__renderToDomTree:xn,__renderToHTMLTree:function(t,e){var r=new u(e);try{return function(t,e,r){var n=ce(t,ke(r)),a=Dt.makeSpan(["katex"],[n]);return Se(a,r)}(pn(t,r),0,r)}catch(e){return gn(e,t,r)}},__setFontMetrics:function(t,e){H[t]=e},__defineSymbol:W,__defineMacro:en,__domTree:{Span:q,Anchor:E,SymbolNode:I,SvgNode:R,PathNode:L,LineNode:D}};e.default=vn}]).default});