/*
Copyright (c) 2003-2014, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/

/*
dialog.css
============

This file styles dialogs and all widgets available inside of it (tabs, buttons,
fields, etc.).

Dialogs are a complex system because they're very flexible. The CKEditor API
makes it easy to create and customize dialogs by code, by making use of several
different widgets inside its contents.

All dialogs share a main dialog strucuture, which can be visually represented
as follows:

+-- .cke_dialog -------------------------------------------------+
| +-- .cke_dialog_body ----------------------------------------+ |
| | +-- .cke_dialog_title --+ +-- .cke_dialog_close_button --+ | |
| | |                       | |                              | | |
| | +-----------------------+ +------------------------------+ | |
| | +-- .cke_dialog_tabs ------------------------------------+ | |
| | |                                                        | | |
| | +--------------------------------------------------------+ | |
| | +-- .cke_dialog_contents --------------------------------+ | |
| | | +-- .cke_dialog_contents_body -----------------------+ | | |
| | | |                                                    | | | |
| | | +----------------------------------------------------+ | | |
| | | +-- .cke_dialog_footer ------------------------------+ | | |
| | | |                                                    | | | |
| | | +----------------------------------------------------+ | | |
| | +--------------------------------------------------------+ | |
| +------------------------------------------------------------+ |
+----------------------------------------------------------------+

/* Config files, where variables are defined */
@import "../config/config";

/* Comments in this file will give more details about each of the above blocks.
*/

/* The outer container of the dialog. */
.cke_dialog {
    /* Mandatory: Because the dialog.css file is loaded on demand, we avoid
        showing an unstyled dialog by hidding it. Here, we restore its visibility. */
    visibility: visible;
}

/* The inner boundary container. */
.cke_dialog_body {
    z-index: 1;
    background: $gray-lighter;
    border: 1px solid $hr-border;

    border-radius: $border-radius;
}

/* This one is required by Firefox 3.6. Without it,
   dialog tabs and resizer float outside of the dialog.
   Although this rule doesn't seem to break anything on other
   browsers, it doesn't work with broken jQueryUI - #9851. */
.cke_browser_gecko19 .cke_dialog_body {
    position: relative;
}

/* Due to our reset we have to recover the styles of some elements. */
.cke_dialog strong {
    font-weight: bold;
}

/* The dialog title. */
.cke_dialog_title {
    font-weight: bold;
    font-size: 13px;
    cursor: move;
    position: relative;
    color: $text-color;
    border-bottom: 1px solid $hr-border;
    padding: 10px 12px;
    background: $gray-lighter;
}

/* The outer part of the dialog contants, which contains the contents body
   and the footer. */
.cke_dialog_contents {
    background-color: #fff;
    overflow: auto;
    padding: 15px 10px 5px 10px;
    margin-top: 35px;
    border-top: 1px solid $hr-border;
    border-radius: 0 0 $border-radius $border-radius;
}

/* The contents body part, which will hold all elements available in the dialog. */
.cke_dialog_contents_body {
    overflow: auto;
    padding: 17px 10px 5px 10px;
    margin-top: 22px;
}

/* The dialog footer, which usually contains the "Ok" and "Cancel" buttons as
   well as a resize handler. */
.cke_dialog_footer {
    text-align: right;
    position: relative;
    border-radius: 0 0 $border-radius $border-radius;
    border-top: 1px solid $hr-border;
    background: $gray-lighter;
}

.cke_rtl .cke_dialog_footer {
    text-align: left;
}

.cke_hc .cke_dialog_footer {
    outline: none;
    border-top: 1px solid #fff;
}

.cke_dialog .cke_resizer {
    margin-top: 28px;
}

.cke_dialog .cke_resizer_rtl {
    margin-left: 5px;
}

.cke_dialog .cke_resizer_ltr {
    margin-right: 5px;
}

/*
Dialog tabs
-------------

Tabs are presented on some of the dialogs to make it possible to have its
contents split on different groups, visible one after the other.

The main element that holds the tabs can be made hidden, in case of no tabs
available.

The following is the visual representation of the tabs block:

+-- .cke_dialog_tabs ------------------------------------+
|  +-- .cke_dialog_tab --+ +-- .cke_dialog_tab --+ ...   |
|  |                     | |                     |       |
|  +---------------------+ +---------------------+       |
+--------------------------------------------------------+

The .cke_dialog_tab_selected class is appended to the active tab.
*/

/* The main tabs container. */
.cke_dialog_tabs {
    height: 24px;
    display: inline-block;
    margin: 10px 0 0;
    position: absolute;
    z-index: 2;
    left: 10px;
}

.cke_rtl .cke_dialog_tabs {
    right: 10px;
}

/* A single tab (an <a> element). */
a.cke_dialog_tab {
    height: 16px;
    padding: 4px 8px;
    margin-right: 3px;
    display: inline-block;
    cursor: pointer;
    line-height: 16px;
    outline: none;
    color: $gray-dark;
    border: 1px solid $hr-border;

    border-radius: 3px 3px 0 0;
    background: lighten($gray-lighter, 2%);
}

.cke_rtl a.cke_dialog_tab {
    margin-right: 0;
    margin-left: 3px;
}

/* A hover state of a regular inactive tab. */
a.cke_dialog_tab:hover {
    background: $gray-light;
    text-decoration: none;
}

a.cke_dialog_tab_selected
{
    background: #fff;
    color: $text-color;
    border-bottom-color: #fff;
    cursor: default;
    filter: none;
}

/* A hover state for selected tab. */
a.cke_dialog_tab_selected:hover {
    background: #fff;
}

.cke_hc a.cke_dialog_tab:hover,
.cke_hc a.cke_dialog_tab_selected
{
    border: 3px solid;
    padding: 2px 6px;
}

a.cke_dialog_tab_disabled
{
    color: #bababa;
    cursor: default;
}

/* selectbox inside tabs container */
.cke_dialog_tabs {
    .cke_dialog_ui_input_select {
        top: -7px !important;
    }
}

/* The .cke_single_page class is appended to the dialog outer element in case
   of dialogs that has no tabs. */
.cke_single_page .cke_dialog_tabs
{
    display: none;
}

.cke_single_page .cke_dialog_contents
{
    padding-top: 5px;
    margin-top: 0;
    border-top: none;
}

/* The close button at the top of the dialog. */

.cke_dialog_close_button {
    background-image: url(images/close.png);
    background-repeat: no-repeat;
    background-position: 0 0;
    position: absolute;
    cursor: pointer;
    text-align: center;
    height: 20px;
    width: 20px;
    top: 9px;
    z-index: 5;
}

.cke_hidpi .cke_dialog_close_button {
    background-image: url(images/hidpi/close.png);
    background-size: 16px;
}

.cke_dialog_close_button span {
    display: none;
}

.cke_hc .cke_dialog_close_button span {
    display: inline;
    cursor: pointer;
    font-weight: bold;
    position: relative;
    top: 3px;
}

.cke_ltr .cke_dialog_close_button {
    right: 5px;
}

.cke_rtl .cke_dialog_close_button {
    left: 6px;
}


/*
Dialog UI Elements
--------------------

The remaining styles define the UI elements that can be used inside dialog
contents.

Most of the UI elements on dialogs contain a textual label. All of them share
the same labelling structure, having the label text inside an element with
.cke_dialog_ui_labeled_label and the element specific part inside the
.cke_dialog_ui_labeled_content class.
*/

/* If an element is supposed to be disabled, the .cke_disabled class is
   appended to it. */
div.cke_disabled .cke_dialog_ui_labeled_content div * {
    background-color: #ddd;
    cursor: default;
}

/*
Horizontal-Box and Vertical-Box
---------------------------------

There are basic layou element used by the editor to properly align elements in
the dialog. They're basically tables that have each cell filled by UI elements.

The following is the visual representation of a H-Box:

+-- .cke_dialog_ui_hbox --------------------------------------------------------------------------------+
|  +-- .cke_dialog_ui_hbox_first --+ +-- .cke_dialog_ui_hbox_child --+ +-- .cke_dialog_ui_hbox_last --+ |
|  +                               + +                               + +                              + |
|  +-------------------------------+ +-------------------------------+ +------------------------------+ |
+-------------------------------------------------------------------------------------------------------+

It is possible to have nested V/H-Boxes.
*/

.cke_dialog_ui_vbox, .cke_dialog_ui_hbox {
    table {
        margin: auto;
    }
}
.cke_dialog_ui_vbox {
    margin-top: 5px;
}
.cke_dialog_ui_vbox_child {
    padding: 5px 0px;
}

.cke_dialog_ui_hbox {
    width: 100%;
}

.cke_dialog_ui_hbox_first,
.cke_dialog_ui_hbox_child,
.cke_dialog_ui_hbox_last {
    vertical-align: top;
}

/* To center a horizontal label-input (selection field dialog / find and replace) */
.cke_dialog_ui_hbox_first,
.cke_dialog_ui_hbox_last {
    > .cke_dialog_ui_labeled_label, > .cke_dialog_ui_html {
        line-height: 30px;
    }
}

.cke_ltr .cke_dialog_ui_hbox_first,
.cke_ltr .cke_dialog_ui_hbox_child {
    padding-right: 10px;
}

.cke_rtl .cke_dialog_ui_hbox_first,
.cke_rtl .cke_dialog_ui_hbox_child {
    padding-left: 10px;
}

.cke_ltr .cke_dialog_footer_buttons .cke_dialog_ui_hbox_first,
.cke_ltr .cke_dialog_footer_buttons .cke_dialog_ui_hbox_child {
    padding-right: 5px;
}

.cke_rtl .cke_dialog_footer_buttons .cke_dialog_ui_hbox_first,
.cke_rtl .cke_dialog_footer_buttons .cke_dialog_ui_hbox_child {
    padding-left: 5px;
    padding-right: 0;
}

/* Applies to all labeled dialog fields */
.cke_hc div {
    &.cke_dialog_ui_input_text,
    &.cke_dialog_ui_input_password,
    &.cke_dialog_ui_input_textarea,
    &.cke_dialog_ui_input_select,
    &.cke_dialog_ui_input_file {
        border: 1px solid;
    }
}

/*
Text Input
------------

The basic text field to input text.

+-- .cke_dialog_ui_text --------------------------+
|  +-- .cke_dialog_ui_labeled_label ------------+ |
|  |                                            | |
|  +--------------------------------------------+ |
|  +-- .cke_dialog_ui_labeled_content ----------+ |
|  | +-- div.cke_dialog_ui_input_text --------+ | |
|  | | +-- input.cke_dialog_ui_input_text --+ | | |
|  | | |                                    | | | |
|  | | +------------------------------------+ | | |
|  | +----------------------------------------+ | |
|  +--------------------------------------------+ |
+-------------------------------------------------+
*/

.cke_dialog_ui_text {
    margin-bottom: 7px;
}
.cke_dialog_ui_select {
    height: auto !important;
    margin-bottom: 7px;
}

/*
Textarea
----------

The textarea field to input larger text.

+-- .cke_dialog_ui_textarea --------------------------+
|  +-- .cke_dialog_ui_labeled_label ----------------+ |
|  |                                                | |
|  +------------------------------------------------+ |
|  +-- .cke_dialog_ui_labeled_content --------------+ |
|  | +-- div.cke_dialog_ui_input_textarea --------+ | |
|  | | +-- input.cke_dialog_ui_input_textarea --+ | | |
|  | | |                                        | | | |
|  | | +----------------------------------------+ | | |
|  | +--------------------------------------------+ | |
|  +------------------------------------------------+ |
+-----------------------------------------------------+
*/

textarea.cke_dialog_ui_input_textarea {
    overflow: auto;
    resize: none;
}

input.cke_dialog_ui_input_text,
input.cke_dialog_ui_input_password,
textarea.cke_dialog_ui_input_textarea {
    @extend %input-style;
}


/*
Button
--------

The buttons used in the dialog footer or inside the contents.

+-- a.cke_dialog_ui_button -----------+
|  +-- span.cke_dialog_ui_button --+  |
|  |                               |  |
|  +-------------------------------+  |
+-------------------------------------+
*/

/* The outer part of the button. */
a.cke_dialog_ui_button {
    display: inline-block;
    *display: inline;
    *zoom: 1;

    padding: 3px 0;
    margin: 0;

    text-align: center;
    color: $text-color;
    vertical-align: middle;
    cursor: pointer;

    border: 1px solid $hr-border;
    border-radius: $border-radius;
    background: #fff;

    &:hover, &:focus, &:active {
        border-color: $gray;
        background-color: $gray-lighter;
        text-decoration: none;
    }
}

/* Buttons inside the content */
.cke_dialog_page_contents {
    a.cke_dialog_ui_button {
        height: 22px;
        line-height: 22px;
        background-color: #f4f4f4;
        &:hover, &:focus, &:active {
            background-color: $gray-lighter;
        }
    }
}

span.cke_dialog_ui_button {
    padding: 0 12px;
}

.cke_hc a.cke_dialog_ui_button {
    &:hover, &:focus, &:active {
        border: 3px solid;
        padding-top: 1px;
        padding-bottom: 1px;

        span {
            padding-left: 10px;
            padding-right: 10px;
        }
    }
}

/*
a.cke_dialog_ui_button[style*="width"]
{
    display: block !important;
    width: auto !important;
}
*/
/* The inner part of the button (both in dialog tabs and dialog footer). */
.cke_dialog_footer_buttons a.cke_dialog_ui_button span {
    color: inherit;
    font-size: 12px;
    line-height: 20px;
}

/* Special class appended to the Ok button. */
a.cke_dialog_ui_button_ok {
    color: #fff;
    border-color: $success-border;
    background: $success;
    &:hover, &:focus, &:active {
        border-color: $success-border-hover;
        background: $success-hover;
    }
}

/* Special class appended to the Cancel button. */
a.cke_dialog_ui_button_cancel {
    background-color: #fff;
    &:focus {
        outline: none;
    }
}

span.cke_dialog_ui_button {
    cursor: pointer;
}

/* A special container that holds the footer buttons. */
.cke_dialog_footer_buttons {
    display: inline-table;
    margin: 10px;
    width: auto;
    position: relative;
    vertical-align: middle;
}

/*
Styles for other dialog element types.
*/

div.cke_dialog_ui_input_select {
    display: table;
}

select.cke_dialog_ui_input_select {
    height: 30px;
    line-height: 30px;

    background-color: #fff;
    padding: 4px 10px;
    border: 1px solid $hr-border;

    outline: none;
    border-radius: $border-radius;

    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
}

.cke_dialog_ui_input_file {
    width: 100%;
    height: 30px;
}

.cke_hc .cke_dialog_ui_labeled_content {
    input, select, textarea {
        &:focus {
            outline: 1px dotted;
        }
    }
}

/*
 * Some utility CSS classes for dialog authors.
 */
.cke_dialog {
    .cke_dark_background {
        background-color: $gray-lighter;
    }
    .cke_light_background {
        background-color: $gray-lighter;
    }
    .cke_centered {
        text-align: center;
    }

    a.cke_btn_reset {
        float: right;
        background: url(images/refresh.png) top left no-repeat;
        width: 16px;
        height: 16px;
        border: 1px none;
        font-size: 1px;
    }

    a.cke_btn_locked, a.cke_btn_unlocked {
        float: left;
        width: 16px;
        height: 16px;
        background-repeat: no-repeat;
        border: none 1px;
        font-size: 1px;
    }
    a.cke_btn_locked {
        background-image: url(images/lock.png);
        .cke_icon {
            display: none;
        }
    }
    a.cke_btn_unlocked {
        background-image: url(images/lock-open.png);
    }

    .cke_btn_over {
        border: outset 1px;
        cursor: pointer;
    }
}

.cke_hidpi .cke_dialog {
    a.cke_btn_reset {
        background-size: 16px;
        background-image: url(images/hidpi/refresh.png);
    }
    a.cke_btn_unlocked, a.cke_btn_locked {
        background-size: 16px;
    }
    a.cke_btn_locked {
        background-image: url(images/hidpi/lock.png);
    }
    a.cke_btn_unlocked {
        background-image: url(images/hidpi/lock-open.png);
    }
}

.cke_rtl .cke_dialog {
    a.cke_btn_reset {
        float: left;
    }
    a.cke_btn_locked, a.cke_btn_unlocked {
        float: right;
    }
}

/*
The rest of the file contains style used on several common plugins. There is a
tendency that these will be moved to the plugins code in the future.
*/

.cke_dialog {
    .ImagePreviewBox, .FlashPreviewBox {
        border: 1px solid $gray;
        border-radius: $border-radius;
        padding: 6px 10px;
        margin-top: 5px;
        background-color: white;
    }
    .ImagePreviewBox {
        overflow: scroll;
        height: 205px;
        width: 300px;
        table td {
            white-space: normal;
        }
    }
    .FlashPreviewBox {
        white-space: normal;
        overflow: auto;
        height: 160px;
        width: 390px;
    }
    .ImagePreviewLoader {
        position: absolute;
        white-space: normal;
        overflow: hidden;
        height: 160px;
        width: 230px;
        margin: 2px;
        padding: 2px;
        opacity: 0.9;
        filter: alpha(opacity = 90);

        background-color: #e4e4e4;
    }
    .cke_pastetext {
        width: 346px;
        height: 170px;
        textarea {
            width: 340px;
            height: 170px;
            resize: none;
        }
    }
    iframe.cke_pasteframe {
        width: 346px;
        height: 130px;
        background-color: white;
        border: 1px solid #aeb3b9;
        border-radius: $border-radius;
    }
    .cke_hand {
        cursor: pointer;
    }
}

.cke_disabled {
    color: #a0a0a0;
}

.cke_dialog_body {
    .cke_label {
        display: none;
    }
    label {
        display: inline-block;
        margin-bottom: 3px;
        cursor: default;
        &.cke_required {
            font-weight: bold;
        }
    }
}

.cke_dialog_ui_html {
    line-height: 150%;
}

a.cke_smile {
    overflow: hidden;
    display: block;
    text-align: center;
    padding: 0.3em 0;
    img {
        vertical-align: middle;
    }
}

a.cke_specialchar {
    cursor: inherit;
    display: block;
    height: 1.25em;
    padding: 0.2em 0.3em;
    text-align: center;
}

a.cke_smile,
a.cke_specialchar {
    background-color: $gray-lighter;
    border: 1px solid transparent;
    vertical-align: top;
    &:hover, &:focus, &:active {
        background: #fff;
        outline: 0;
    }
    &:hover {
        border-color: $gray;
    }
    &:focus, &:active {
        border-color: $primary;
    }
}

/**
 * Styles specific to "cellProperties" dialog.
 */

.cke_dialog_contents a.colorChooser {
    display: block;
    margin-top: 6px;
    margin-left: 10px;
    width: 80px;
}

.cke_rtl .cke_dialog_contents a.colorChooser {
    margin-right: 10px;
}


.cke_dialog_ui_checkbox {
    display: inline-block;
    margin-bottom: 5px;
}

/* Compensate focus outline for some input elements. (#6200) */
.cke_dialog_ui_checkbox_input:focus,
.cke_dialog_ui_radio_input:focus,
.cke_btn_over {
    outline: 1px dotted #696969;
}

.cke_iframe_shim {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    filter: alpha(opacity = 0);
    width: 100%;
    height: 100%;
}
