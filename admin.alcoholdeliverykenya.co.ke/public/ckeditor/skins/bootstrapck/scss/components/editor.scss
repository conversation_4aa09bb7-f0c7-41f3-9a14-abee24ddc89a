/*
Copyright (c) 2003-2014, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/

/*
editor.css
============

This is he heart of the skin system. This is the file loaded by the editor to
style all elements inside its main interface.

To make it easier to maintain, instead of including all styles here, we import
other files.
*/

/* Config files, where variables are defined */
@import "../config/config";

/* "Reset" styles, necessary to avoid the editor UI being broken by external CSS. */
@import "reset";

/* Styles the main interface structure (holding box). */
@import "mainui";

/* Styles all "panels", which are the floating elements that appear when
   opening toolbar combos, menu buttons, context menus, etc. */
@import "panel";

/* Styles the color panel displayed by the color buttons. */
@import "colorpanel";

/* Styles to toolbar. */
@import "toolbar";

/* Styles menus, which are lists of selectable items (context menu, menu button). */
@import "menu";

/* Styles toolbar combos. */
@import "richcombo";

/* Styles the elements path bar, available at the bottom of the editor UI.*/
@import "elementspath";

/* Contains hard-coded presets for "configurable-like" options of the UI
   (e.g. display labels on specific buttons) */
@import "presets";

/* Important!
   To avoid showing the editor UI while its styles are still not available, the
   editor creates it with visibility:hidden. Here, we restore the UI visibility. */
.cke_chrome {
    visibility: inherit;
}

/* For accessibility purposes, several "voice labels" are present in the UI.
   These are usually <span> elements that show not be visible, but that are
   used by screen-readers to announce other elements. Here, we hide these
   <spans>, in fact. */
.cke_voice_label {
    display: none;
}

legend.cke_voice_label {
    display: none;
}
